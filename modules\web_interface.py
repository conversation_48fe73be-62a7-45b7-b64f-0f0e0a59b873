"""
Web Interface Module
Provides Flask-based web interface for system configuration and monitoring
"""

import logging
import json
from flask import Flask, render_template, request, jsonify, redirect, url_for
from flask_socketio import SocketIO, emit
import threading
import time
from typing import Dict, Any

class WebInterface:
    """Flask web interface for voice and gesture control system"""
    
    def __init__(self, config: Dict[str, Any], main_app):
        self.config = config
        self.main_app = main_app
        self.logger = logging.getLogger(__name__)
        
        # Web configuration
        self.web_config = config.get('web', {})
        self.host = self.web_config.get('host', '127.0.0.1')
        self.port = self.web_config.get('port', 5000)
        self.debug = self.web_config.get('debug', False)
        
        # Initialize Flask app
        self.app = Flask(__name__, 
                        template_folder='../templates',
                        static_folder='../static')
        self.app.config['SECRET_KEY'] = 'voice_gesture_control_secret'
        
        # Initialize SocketIO
        self.socketio = SocketIO(self.app, cors_allowed_origins="*")
        
        # Setup routes
        self.setup_routes()
        self.setup_socketio_events()

        # Status update thread
        self.status_thread = None
        self.running = False

        # Setup activity manager callback for real-time updates
        if hasattr(self.main_app, 'activity_manager'):
            self.main_app.activity_manager.add_callback(self._on_new_activity)

        self.logger.info("Web Interface initialized")
    
    def setup_routes(self):
        """Setup Flask routes"""
        
        @self.app.route('/')
        def dashboard():
            """Main dashboard"""
            return render_template('dashboard.html')
        
        @self.app.route('/api/status')
        def get_status():
            """Get system status"""
            try:
                status = {
                    'system': {
                        'running': getattr(self.main_app, 'running', False),
                        'voice_enabled': getattr(self.main_app, 'voice_enabled', False),
                        'gesture_enabled': getattr(self.main_app, 'gesture_enabled', False)
                    },
                    'voice': self.main_app.voice_controller.get_status() if hasattr(self.main_app, 'voice_controller') else {},
                    'gesture': self.main_app.gesture_controller.get_status() if hasattr(self.main_app, 'gesture_controller') else {},
                    'commands': self.main_app.command_executor.get_available_commands() if hasattr(self.main_app, 'command_executor') else {}
                }
                return jsonify(status)
            except Exception as e:
                self.logger.error(f"Error getting status: {e}")
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/control/<action>', methods=['POST'])
        def control_system(action):
            """Control system actions"""
            try:
                if action == 'start':
                    self.main_app.start()
                    return jsonify({'success': True, 'message': 'System started'})
                elif action == 'stop':
                    self.main_app.stop()
                    return jsonify({'success': True, 'message': 'System stopped'})
                elif action == 'toggle_voice':
                    self.main_app.toggle_voice_control()
                    status = 'enabled' if self.main_app.voice_enabled else 'disabled'
                    return jsonify({'success': True, 'message': f'Voice control {status}'})
                elif action == 'toggle_gesture':
                    self.main_app.toggle_gesture_control()
                    status = 'enabled' if self.main_app.gesture_enabled else 'disabled'
                    return jsonify({'success': True, 'message': f'Gesture control {status}'})
                else:
                    return jsonify({'error': 'Unknown action'}), 400
            except Exception as e:
                self.logger.error(f"Error controlling system: {e}")
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/commands')
        def get_commands():
            """Get available commands"""
            try:
                commands = self.main_app.command_executor.get_available_commands()
                return jsonify(commands)
            except Exception as e:
                self.logger.error(f"Error getting commands: {e}")
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/commands', methods=['POST'])
        def add_command():
            """Add new command"""
            try:
                data = request.get_json()
                command_type = data.get('type')  # 'voice' or 'gesture'
                command = data.get('command')
                action = data.get('action')
                
                if not all([command_type, command, action]):
                    return jsonify({'error': 'Missing required fields'}), 400
                
                if command_type == 'voice':
                    self.main_app.command_executor.add_voice_command(command, action)
                elif command_type == 'gesture':
                    self.main_app.command_executor.add_gesture_command(command, action)
                else:
                    return jsonify({'error': 'Invalid command type'}), 400
                
                return jsonify({'success': True, 'message': f'{command_type.title()} command added'})
                
            except Exception as e:
                self.logger.error(f"Error adding command: {e}")
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/test/<component>')
        def test_component(component):
            """Test system components"""
            try:
                if component == 'microphone':
                    success, message = self.main_app.voice_controller.test_microphone()
                elif component == 'camera':
                    success, message = self.main_app.gesture_controller.test_camera()
                else:
                    return jsonify({'error': 'Unknown component'}), 400
                
                return jsonify({'success': success, 'message': message})
                
            except Exception as e:
                self.logger.error(f"Error testing {component}: {e}")
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/settings')
        def settings():
            """Settings page"""
            return render_template('settings.html', config=self.config)
        
        @self.app.route('/api/config', methods=['GET', 'POST'])
        def handle_config():
            """Get or update configuration"""
            if request.method == 'GET':
                return jsonify(self.config)
            else:
                try:
                    new_config = request.get_json()
                    # Update configuration (in a real app, you'd save to file)
                    self.config.update(new_config)
                    return jsonify({'success': True, 'message': 'Configuration updated'})
                except Exception as e:
                    self.logger.error(f"Error updating config: {e}")
                    return jsonify({'error': str(e)}), 500

        @self.app.route('/api/activities')
        def get_activities():
            """Get activity history"""
            try:
                limit = request.args.get('limit', type=int)
                activity_type = request.args.get('type')
                since = request.args.get('since', type=float)

                activities = self.main_app.activity_manager.get_activities(
                    limit=limit,
                    activity_type=activity_type,
                    since=since
                )
                return jsonify(activities)
            except Exception as e:
                self.logger.error(f"Error getting activities: {e}")
                return jsonify({'error': str(e)}), 500

        @self.app.route('/api/activities/recent')
        def get_recent_activities():
            """Get recent activities"""
            try:
                minutes = request.args.get('minutes', 10, type=int)
                activities = self.main_app.activity_manager.get_recent_activities(minutes)
                return jsonify(activities)
            except Exception as e:
                self.logger.error(f"Error getting recent activities: {e}")
                return jsonify({'error': str(e)}), 500

        @self.app.route('/api/activities/search')
        def search_activities():
            """Search activities"""
            try:
                query = request.args.get('q', '')
                limit = request.args.get('limit', 50, type=int)

                if not query:
                    return jsonify({'error': 'Query parameter required'}), 400

                activities = self.main_app.activity_manager.search_activities(query, limit)
                return jsonify(activities)
            except Exception as e:
                self.logger.error(f"Error searching activities: {e}")
                return jsonify({'error': str(e)}), 500

        @self.app.route('/api/activities/stats')
        def get_activity_stats():
            """Get activity statistics"""
            try:
                stats = self.main_app.activity_manager.get_statistics()
                return jsonify(stats)
            except Exception as e:
                self.logger.error(f"Error getting activity stats: {e}")
                return jsonify({'error': str(e)}), 500

        @self.app.route('/api/activities/clear', methods=['POST'])
        def clear_activities():
            """Clear all activities"""
            try:
                self.main_app.activity_manager.clear_activities()
                return jsonify({'success': True, 'message': 'Activities cleared'})
            except Exception as e:
                self.logger.error(f"Error clearing activities: {e}")
                return jsonify({'error': str(e)}), 500

        @self.app.route('/api/images')
        def get_images():
            """Get recent captured images"""
            try:
                count = request.args.get('count', 10, type=int)
                images = self.main_app.image_capture.get_recent_images(count)
                return jsonify(images)
            except Exception as e:
                self.logger.error(f"Error getting images: {e}")
                return jsonify({'error': str(e)}), 500

        @self.app.route('/api/images/stats')
        def get_image_stats():
            """Get image capture statistics"""
            try:
                stats = self.main_app.image_capture.get_image_stats()
                return jsonify(stats)
            except Exception as e:
                self.logger.error(f"Error getting image stats: {e}")
                return jsonify({'error': str(e)}), 500

        @self.app.route('/api/images/clear', methods=['POST'])
        def clear_images():
            """Clear all captured images"""
            try:
                count = self.main_app.image_capture.clear_images()
                return jsonify({'success': True, 'message': f'Cleared {count} images'})
            except Exception as e:
                self.logger.error(f"Error clearing images: {e}")
                return jsonify({'error': str(e)}), 500

        @self.app.route('/api/images/<filename>')
        def serve_image(filename):
            """Serve captured images"""
            try:
                from flask import send_from_directory
                return send_from_directory(self.main_app.image_capture.storage_dir, filename)
            except Exception as e:
                self.logger.error(f"Error serving image: {e}")
                return jsonify({'error': str(e)}), 404
    
    def setup_socketio_events(self):
        """Setup SocketIO events for real-time updates"""
        
        @self.socketio.on('connect')
        def handle_connect():
            """Handle client connection"""
            self.logger.info("Client connected to SocketIO")
            emit('status', self.get_current_status())
        
        @self.socketio.on('disconnect')
        def handle_disconnect():
            """Handle client disconnection"""
            self.logger.info("Client disconnected from SocketIO")
        
        @self.socketio.on('request_status')
        def handle_status_request():
            """Handle status request"""
            emit('status', self.get_current_status())

        @self.socketio.on('request_activities')
        def handle_activities_request(data):
            """Handle activities request"""
            try:
                limit = data.get('limit', 20)
                activity_type = data.get('type')
                activities = self.main_app.activity_manager.get_activities(
                    limit=limit,
                    activity_type=activity_type
                )
                emit('activities', activities)
            except Exception as e:
                self.logger.error(f"Error handling activities request: {e}")
                emit('error', {'message': str(e)})

        @self.socketio.on('request_activity_stats')
        def handle_activity_stats_request():
            """Handle activity statistics request"""
            try:
                stats = self.main_app.activity_manager.get_statistics()
                emit('activity_stats', stats)
            except Exception as e:
                self.logger.error(f"Error handling activity stats request: {e}")
                emit('error', {'message': str(e)})
    
    def get_current_status(self):
        """Get current system status for SocketIO"""
        try:
            return {
                'system': {
                    'running': getattr(self.main_app, 'running', False),
                    'voice_enabled': getattr(self.main_app, 'voice_enabled', False),
                    'gesture_enabled': getattr(self.main_app, 'gesture_enabled', False)
                },
                'voice': self.main_app.voice_controller.get_status() if hasattr(self.main_app, 'voice_controller') else {},
                'gesture': self.main_app.gesture_controller.get_status() if hasattr(self.main_app, 'gesture_controller') else {},
                'timestamp': time.time()
            }
        except Exception as e:
            self.logger.error(f"Error getting current status: {e}")
            return {'error': str(e)}
    
    def start_status_updates(self):
        """Start background status updates"""
        if self.status_thread and self.status_thread.is_alive():
            return
        
        self.status_thread = threading.Thread(target=self._status_update_loop, daemon=True)
        self.status_thread.start()
        self.logger.info("Status update thread started")
    
    def _status_update_loop(self):
        """Background loop for status updates"""
        while self.running:
            try:
                status = self.get_current_status()
                self.socketio.emit('status_update', status)
                time.sleep(2)  # Update every 2 seconds
            except Exception as e:
                self.logger.error(f"Error in status update loop: {e}")
                time.sleep(5)
    
    def run(self):
        """Run the web interface"""
        try:
            self.running = True
            self.start_status_updates()
            
            self.logger.info(f"Starting web interface on {self.host}:{self.port}")
            self.socketio.run(self.app, 
                            host=self.host, 
                            port=self.port, 
                            debug=self.debug,
                            use_reloader=False)
        except Exception as e:
            self.logger.error(f"Error running web interface: {e}")
    
    def stop(self):
        """Stop the web interface"""
        self.running = False
        self.logger.info("Web interface stopped")

    def _on_new_activity(self, activity_data):
        """Callback for new activity events"""
        try:
            # Emit new activity to all connected clients
            self.socketio.emit('new_activity', activity_data)

            # Also emit updated statistics
            if hasattr(self.main_app, 'activity_manager'):
                stats = self.main_app.activity_manager.get_statistics()
                self.socketio.emit('activity_stats_update', stats)

        except Exception as e:
            self.logger.error(f"Error broadcasting new activity: {e}")
