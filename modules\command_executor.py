"""
Command Execution System
Handles mapping and execution of voice/gesture commands to system actions
"""

import logging
import subprocess
import pyautogui
import webbrowser
import os
import time
from typing import Dict, Any

class CommandExecutor:
    """Executes system commands based on voice/gesture inputs"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Configure PyAutoGUI
        pyautogui.FAILSAFE = True
        pyautogui.PAUSE = 0.1
        
        # Command mappings
        self.voice_commands = config.get('commands', {}).get('voice', {})
        self.gesture_commands = config.get('commands', {}).get('gesture', {})
        
        self.logger.info("Command Executor initialized")
    
    def execute_voice_command(self, command: str) -> bool:
        """Execute a voice command"""
        command = command.lower().strip()
        
        if command in self.voice_commands:
            action = self.voice_commands[command]
            return self._execute_action(action, source="voice", command=command)
        else:
            self.logger.warning(f"Unknown voice command: {command}")
            return False
    
    def execute_gesture_command(self, gesture: str) -> bool:
        """Execute a gesture command"""
        gesture = gesture.lower().strip()
        
        if gesture in self.gesture_commands:
            action = self.gesture_commands[gesture]
            return self._execute_action(action, source="gesture", command=gesture)
        else:
            self.logger.warning(f"Unknown gesture command: {gesture}")
            return False
    
    def _execute_action(self, action: str, source: str, command: str) -> bool:
        """Execute the specified action"""
        try:
            self.logger.info(f"Executing {source} command '{command}' -> action '{action}'")
            
            if action == "open_browser":
                self._open_browser()
            elif action == "scroll_down":
                self._scroll_down()
            elif action == "scroll_up":
                self._scroll_up()
            elif action == "volume_up":
                self._volume_up()
            elif action == "volume_down":
                self._volume_down()
            elif action == "close_window":
                self._close_window()
            elif action == "minimize_window":
                self._minimize_window()
            elif action == "pause_media":
                self._pause_media()
            elif action == "play_media":
                self._play_media()
            elif action == "screenshot":
                self._take_screenshot()
            else:
                self.logger.error(f"Unknown action: {action}")
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error executing action '{action}': {e}")
            return False
    
    def _open_browser(self):
        """Open default web browser"""
        webbrowser.open('https://www.google.com')
        self.logger.info("Opened web browser")
    
    def _scroll_down(self):
        """Scroll down on current window"""
        pyautogui.scroll(-3)
        self.logger.info("Scrolled down")
    
    def _scroll_up(self):
        """Scroll up on current window"""
        pyautogui.scroll(3)
        self.logger.info("Scrolled up")
    
    def _volume_up(self):
        """Increase system volume"""
        try:
            # Use keyboard shortcut - works on all platforms
            pyautogui.press('volumeup')
            self.logger.info("Volume increased")
        except Exception as e:
            self.logger.error(f"Volume up failed: {e}")

    def _volume_down(self):
        """Decrease system volume"""
        try:
            # Use keyboard shortcut - works on all platforms
            pyautogui.press('volumedown')
            self.logger.info("Volume decreased")
        except Exception as e:
            self.logger.error(f"Volume down failed: {e}")
    
    def _close_window(self):
        """Close current window"""
        pyautogui.hotkey('alt', 'f4')
        self.logger.info("Closed window")
    
    def _minimize_window(self):
        """Minimize current window"""
        pyautogui.hotkey('win', 'down')
        self.logger.info("Minimized window")
    
    def _pause_media(self):
        """Pause media playback"""
        pyautogui.press('space')
        self.logger.info("Paused media")
    
    def _play_media(self):
        """Play/resume media playback"""
        pyautogui.press('space')
        self.logger.info("Played media")
    
    def _take_screenshot(self):
        """Take a screenshot"""
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        filename = f"screenshot_{timestamp}.png"
        screenshot = pyautogui.screenshot()
        screenshot.save(filename)
        self.logger.info(f"Screenshot saved as {filename}")
    
    def add_voice_command(self, command: str, action: str):
        """Add a new voice command mapping"""
        self.voice_commands[command.lower()] = action
        self.logger.info(f"Added voice command: '{command}' -> '{action}'")
    
    def add_gesture_command(self, gesture: str, action: str):
        """Add a new gesture command mapping"""
        self.gesture_commands[gesture.lower()] = action
        self.logger.info(f"Added gesture command: '{gesture}' -> '{action}'")
    
    def get_available_commands(self):
        """Get all available commands"""
        return {
            'voice': list(self.voice_commands.keys()),
            'gesture': list(self.gesture_commands.keys())
        }
