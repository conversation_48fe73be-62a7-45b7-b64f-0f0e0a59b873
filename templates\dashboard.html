<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Voice & Gesture Control Dashboard</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .status-bar {
            display: flex;
            justify-content: space-around;
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #dee2e6;
        }
        
        .status-item {
            text-align: center;
            padding: 15px;
            border-radius: 10px;
            background: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            min-width: 150px;
        }
        
        .status-item.active {
            background: #d4edda;
            border: 2px solid #28a745;
        }
        
        .status-item.inactive {
            background: #f8d7da;
            border: 2px solid #dc3545;
        }
        
        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            padding: 30px;
        }

        .activity-section {
            grid-column: 1 / -1;
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
            margin-top: 20px;
        }
        
        .control-panel, .command-panel, .activity-panel, .stats-panel {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .control-panel h2, .command-panel h2, .activity-panel h2, .stats-panel h2 {
            margin-bottom: 20px;
            color: #333;
            border-bottom: 2px solid #4facfe;
            padding-bottom: 10px;
        }
        
        .control-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
            text-transform: uppercase;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background: #1e7e34;
            transform: translateY(-2px);
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .btn-danger:hover {
            background: #c82333;
            transform: translateY(-2px);
        }
        
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        
        .btn-warning:hover {
            background: #e0a800;
            transform: translateY(-2px);
        }
        
        .command-list {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            background: white;
        }
        
        .command-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            border-bottom: 1px solid #eee;
            margin-bottom: 10px;
            border-radius: 5px;
            background: #f8f9fa;
        }
        
        .command-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        
        .test-results {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            background: white;
            border: 1px solid #dee2e6;
        }
        
        .alert {
            padding: 12px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .activity-feed {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            background: white;
        }

        .activity-item {
            padding: 15px;
            border-bottom: 1px solid #eee;
            transition: background-color 0.3s ease;
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-item:hover {
            background-color: #f8f9fa;
        }

        .activity-item.new-activity {
            background-color: #d4edda;
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .activity-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .activity-type {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }

        .activity-type.gesture {
            background: #e3f2fd;
            color: #1976d2;
        }

        .activity-type.voice {
            background: #f3e5f5;
            color: #7b1fa2;
        }

        .activity-type.system {
            background: #e8f5e8;
            color: #388e3c;
        }

        .activity-time {
            font-size: 12px;
            color: #666;
        }

        .activity-action {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }

        .activity-details {
            font-size: 14px;
            color: #666;
        }

        .activity-status {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .activity-status.success {
            background: #28a745;
        }

        .activity-status.failed {
            background: #dc3545;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .stat-item {
            text-align: center;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }

        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #4facfe;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 12px;
            color: #666;
            text-transform: uppercase;
        }

        .activity-controls {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }

        .filter-btn {
            padding: 8px 16px;
            border: 1px solid #dee2e6;
            background: white;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .filter-btn:hover {
            background: #f8f9fa;
        }

        .filter-btn.active {
            background: #4facfe;
            color: white;
            border-color: #4facfe;
        }

        .search-box {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #dee2e6;
            border-radius: 20px;
            font-size: 14px;
        }
        
        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }

            .activity-section {
                grid-template-columns: 1fr;
            }

            .status-bar {
                flex-direction: column;
                gap: 10px;
            }

            .control-buttons {
                grid-template-columns: 1fr;
            }

            .stats-grid {
                grid-template-columns: 1fr 1fr;
            }

            .activity-controls {
                flex-direction: column;
                gap: 10px;
            }

            .filter-btn {
                flex: 1;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎤 Voice & Gesture Control</h1>
            <p>Hands-free laptop control system</p>
        </div>
        
        <div class="status-bar">
            <div class="status-item" id="system-status">
                <h3>System</h3>
                <p id="system-text">Loading...</p>
            </div>
            <div class="status-item" id="voice-status">
                <h3>Voice Control</h3>
                <p id="voice-text">Loading...</p>
            </div>
            <div class="status-item" id="gesture-status">
                <h3>Gesture Control</h3>
                <p id="gesture-text">Loading...</p>
            </div>
        </div>
        
        <div class="main-content">
            <div class="control-panel">
                <h2>🎛️ System Controls</h2>
                
                <div class="control-buttons">
                    <button class="btn btn-success" onclick="controlSystem('start')">Start System</button>
                    <button class="btn btn-danger" onclick="controlSystem('stop')">Stop System</button>
                    <button class="btn btn-primary" onclick="controlSystem('toggle_voice')">Toggle Voice</button>
                    <button class="btn btn-primary" onclick="controlSystem('toggle_gesture')">Toggle Gesture</button>
                </div>
                
                <h3>🧪 Component Tests</h3>
                <div class="control-buttons">
                    <button class="btn btn-warning" onclick="testComponent('microphone')">Test Microphone</button>
                    <button class="btn btn-warning" onclick="testComponent('camera')">Test Camera</button>
                </div>
                
                <div class="test-results" id="test-results" style="display: none;">
                    <h4>Test Results:</h4>
                    <div id="test-output"></div>
                </div>
            </div>
            
            <div class="command-panel">
                <h2>📋 Available Commands</h2>
                
                <h3>Voice Commands</h3>
                <div class="command-list" id="voice-commands">
                    Loading commands...
                </div>
                
                <h3>Gesture Commands</h3>
                <div class="command-list" id="gesture-commands">
                    Loading commands...
                </div>
            </div>
        </div>

        <div class="activity-section">
            <div class="activity-panel">
                <h2>📊 Real-time Activity Feed</h2>

                <div class="activity-controls">
                    <button class="filter-btn active" data-filter="all">All</button>
                    <button class="filter-btn" data-filter="gesture">Gestures</button>
                    <button class="filter-btn" data-filter="voice">Voice</button>
                    <button class="filter-btn" data-filter="system">System</button>
                    <select class="filter-btn" id="time-filter" style="border-radius: 20px; padding: 8px 12px;">
                        <option value="all">All Time</option>
                        <option value="5">Last 5 minutes</option>
                        <option value="30">Last 30 minutes</option>
                        <option value="60">Last hour</option>
                        <option value="1440">Last 24 hours</option>
                    </select>
                    <input type="text" class="search-box" placeholder="Search activities..." id="activity-search">
                </div>

                <div class="activity-feed" id="activity-feed">
                    <div style="text-align: center; padding: 40px; color: #666;">
                        <p>No activities yet. Start using voice commands or gestures!</p>
                    </div>
                </div>
            </div>

            <div class="stats-panel">
                <h2>📈 Activity Statistics</h2>

                <div class="stats-grid" id="stats-grid">
                    <div class="stat-item">
                        <div class="stat-value" id="total-activities">0</div>
                        <div class="stat-label">Total Activities</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="success-rate">0%</div>
                        <div class="stat-label">Success Rate</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="gesture-count">0</div>
                        <div class="stat-label">Gestures</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="voice-count">0</div>
                        <div class="stat-label">Voice Commands</div>
                    </div>
                </div>

                <div style="margin-top: 20px;">
                    <button class="btn btn-primary" onclick="exportActivities()" style="width: 100%; margin-bottom: 10px;">
                        Export Activities
                    </button>
                    <button class="btn btn-warning" onclick="clearActivities()" style="width: 100%;">
                        Clear Activity History
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // Initialize Socket.IO connection
        const socket = io();
        
        // Socket event handlers
        socket.on('connect', function() {
            console.log('Connected to server');
            loadCommands();
        });
        
        socket.on('status_update', function(data) {
            updateStatus(data);
        });
        
        socket.on('status', function(data) {
            updateStatus(data);
        });

        socket.on('new_activity', function(activity) {
            addActivityToFeed(activity);
        });

        socket.on('activity_stats_update', function(stats) {
            updateActivityStats(stats);
        });

        socket.on('activities', function(activities) {
            displayActivities(activities);
        });
        
        // Update status display
        function updateStatus(data) {
            if (data.system) {
                const systemStatus = document.getElementById('system-status');
                const systemText = document.getElementById('system-text');
                
                if (data.system.running) {
                    systemStatus.className = 'status-item active';
                    systemText.textContent = 'Running';
                } else {
                    systemStatus.className = 'status-item inactive';
                    systemText.textContent = 'Stopped';
                }
                
                // Voice status
                const voiceStatus = document.getElementById('voice-status');
                const voiceText = document.getElementById('voice-text');
                
                if (data.system.voice_enabled) {
                    voiceStatus.className = 'status-item active';
                    voiceText.textContent = 'Enabled';
                } else {
                    voiceStatus.className = 'status-item inactive';
                    voiceText.textContent = 'Disabled';
                }
                
                // Gesture status
                const gestureStatus = document.getElementById('gesture-status');
                const gestureText = document.getElementById('gesture-text');
                
                if (data.system.gesture_enabled) {
                    gestureStatus.className = 'status-item active';
                    gestureText.textContent = 'Enabled';
                } else {
                    gestureStatus.className = 'status-item inactive';
                    gestureText.textContent = 'Disabled';
                }
            }
        }
        
        // Control system functions
        function controlSystem(action) {
            fetch(`/api/control/${action}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert(data.message, 'success');
                } else {
                    showAlert(data.error || 'Operation failed', 'danger');
                }
            })
            .catch(error => {
                showAlert('Network error: ' + error.message, 'danger');
            });
        }
        
        // Test components
        function testComponent(component) {
            const testResults = document.getElementById('test-results');
            const testOutput = document.getElementById('test-output');
            
            testResults.style.display = 'block';
            testOutput.innerHTML = '<div class="alert alert-info">Testing ' + component + '...</div>';
            
            fetch(`/api/test/${component}`)
            .then(response => response.json())
            .then(data => {
                const alertClass = data.success ? 'alert-success' : 'alert-danger';
                testOutput.innerHTML = `<div class="alert ${alertClass}">${data.message}</div>`;
            })
            .catch(error => {
                testOutput.innerHTML = '<div class="alert alert-danger">Test failed: ' + error.message + '</div>';
            });
        }
        
        // Load available commands
        function loadCommands() {
            fetch('/api/commands')
            .then(response => response.json())
            .then(data => {
                displayCommands('voice-commands', data.voice || []);
                displayCommands('gesture-commands', data.gesture || []);
            })
            .catch(error => {
                console.error('Error loading commands:', error);
            });
        }
        
        // Display commands in the UI
        function displayCommands(containerId, commands) {
            const container = document.getElementById(containerId);
            
            if (commands.length === 0) {
                container.innerHTML = '<p>No commands available</p>';
                return;
            }
            
            container.innerHTML = commands.map(command => 
                `<div class="command-item">
                    <span><strong>${command}</strong></span>
                </div>`
            ).join('');
        }
        
        // Show alert messages
        function showAlert(message, type) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type}`;
            alertDiv.textContent = message;
            
            document.body.appendChild(alertDiv);
            
            setTimeout(() => {
                alertDiv.remove();
            }, 5000);
        }
        
        // Activity management functions
        let currentFilter = 'all';
        let allActivities = [];

        function addActivityToFeed(activity) {
            allActivities.unshift(activity); // Add to beginning

            // Keep only last 100 activities in memory
            if (allActivities.length > 100) {
                allActivities = allActivities.slice(0, 100);
            }

            // Apply current filter and display
            displayFilteredActivities();
        }

        function displayActivities(activities) {
            allActivities = activities;
            displayFilteredActivities();
        }

        function displayFilteredActivities() {
            const feed = document.getElementById('activity-feed');
            const searchTerm = document.getElementById('activity-search').value.toLowerCase();
            const timeFilter = document.getElementById('time-filter').value;

            let filteredActivities = allActivities;

            // Apply type filter
            if (currentFilter !== 'all') {
                filteredActivities = filteredActivities.filter(activity =>
                    activity.type === currentFilter
                );
            }

            // Apply time filter
            if (timeFilter !== 'all') {
                const minutesAgo = parseInt(timeFilter);
                const cutoffTime = Date.now() / 1000 - (minutesAgo * 60);
                filteredActivities = filteredActivities.filter(activity =>
                    activity.timestamp >= cutoffTime
                );
            }

            // Apply search filter
            if (searchTerm) {
                filteredActivities = filteredActivities.filter(activity =>
                    activity.action.toLowerCase().includes(searchTerm) ||
                    JSON.stringify(activity.details).toLowerCase().includes(searchTerm)
                );
            }

            if (filteredActivities.length === 0) {
                feed.innerHTML = `
                    <div style="text-align: center; padding: 40px; color: #666;">
                        <p>No activities found matching your criteria.</p>
                    </div>
                `;
                return;
            }

            feed.innerHTML = filteredActivities.map(activity => createActivityHTML(activity)).join('');
        }

        function createActivityHTML(activity) {
            const statusClass = activity.success ? 'success' : 'failed';
            const typeClass = activity.type;

            return `
                <div class="activity-item" data-id="${activity.id}">
                    <div class="activity-header">
                        <div>
                            <span class="activity-type ${typeClass}">${activity.type}</span>
                            <span class="activity-status ${statusClass}"></span>
                        </div>
                        <div class="activity-time">${activity.time_ago}</div>
                    </div>
                    <div class="activity-action">${activity.action}</div>
                    <div class="activity-details">
                        ${formatActivityDetails(activity)}
                    </div>
                </div>
            `;
        }

        function formatActivityDetails(activity) {
            let details = [];

            if (activity.details.action && activity.details.action !== activity.action) {
                details.push(`Action: ${activity.details.action}`);
            }

            if (activity.details.gesture) {
                details.push(`Gesture: ${activity.details.gesture}`);
            }

            if (activity.details.command) {
                details.push(`Command: "${activity.details.command}"`);
            }

            if (activity.duration) {
                details.push(`Duration: ${(activity.duration * 1000).toFixed(0)}ms`);
            }

            return details.join(' • ') || 'No additional details';
        }

        function updateActivityStats(stats) {
            document.getElementById('total-activities').textContent = stats.total_activities || 0;
            document.getElementById('success-rate').textContent = `${(stats.success_rate || 0).toFixed(1)}%`;
            document.getElementById('gesture-count').textContent = stats.gesture_count || 0;
            document.getElementById('voice-count').textContent = stats.voice_count || 0;
        }

        function exportActivities() {
            if (allActivities.length === 0) {
                showAlert('No activities to export', 'info');
                return;
            }

            const csvContent = generateCSV(allActivities);
            const blob = new Blob([csvContent], { type: 'text/csv' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `activities_${new Date().toISOString().split('T')[0]}.csv`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);

            showAlert('Activities exported successfully', 'success');
        }

        function generateCSV(activities) {
            const headers = ['Timestamp', 'Type', 'Action', 'Success', 'Duration (ms)', 'Details'];
            const rows = activities.map(activity => [
                new Date(activity.timestamp * 1000).toISOString(),
                activity.type,
                activity.action,
                activity.success ? 'Yes' : 'No',
                activity.duration ? (activity.duration * 1000).toFixed(0) : '',
                JSON.stringify(activity.details).replace(/"/g, '""')
            ]);

            const csvContent = [headers, ...rows]
                .map(row => row.map(field => `"${field}"`).join(','))
                .join('\n');

            return csvContent;
        }

        function clearActivities() {
            if (confirm('Are you sure you want to clear all activity history?')) {
                fetch('/api/activities/clear', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        allActivities = [];
                        displayFilteredActivities();
                        updateActivityStats({
                            total_activities: 0,
                            success_rate: 0,
                            gesture_count: 0,
                            voice_count: 0
                        });
                        showAlert('Activity history cleared', 'success');
                    } else {
                        showAlert(data.error || 'Failed to clear activities', 'danger');
                    }
                })
                .catch(error => {
                    showAlert('Network error: ' + error.message, 'danger');
                });
            }
        }

        function loadInitialActivities() {
            // Load recent activities
            socket.emit('request_activities', { limit: 50 });

            // Load activity statistics
            socket.emit('request_activity_stats');
        }

        // Setup filter buttons
        document.addEventListener('DOMContentLoaded', function() {
            // Filter button event listeners
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    // Update active state
                    document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
                    this.classList.add('active');

                    // Update filter
                    currentFilter = this.dataset.filter;
                    displayFilteredActivities();
                });
            });

            // Search box event listener
            document.getElementById('activity-search').addEventListener('input', function() {
                displayFilteredActivities();
            });

            // Time filter event listener
            document.getElementById('time-filter').addEventListener('change', function() {
                displayFilteredActivities();
            });

            // Load initial data
            loadCommands();
            loadInitialActivities();
        });
    </script>
</body>
</html>
