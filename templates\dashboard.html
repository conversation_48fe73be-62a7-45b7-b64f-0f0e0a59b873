<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Voice & Gesture Control Dashboard</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-color: #6366f1;
            --primary-dark: #4f46e5;
            --secondary-color: #8b5cf6;
            --accent-color: #06b6d4;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --dark-color: #1f2937;
            --light-color: #f9fafb;
            --border-color: #e5e7eb;
            --text-primary: #111827;
            --text-secondary: #6b7280;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            --border-radius: 12px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            color: var(--text-primary);
            line-height: 1.6;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-xl);
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .header h1 {
            font-size: 3em;
            font-weight: 700;
            margin-bottom: 15px;
            position: relative;
            z-index: 1;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2em;
            font-weight: 400;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }
        
        .status-bar {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            background: var(--light-color);
            padding: 30px;
            border-bottom: 1px solid var(--border-color);
        }

        .status-item {
            text-align: center;
            padding: 25px 20px;
            border-radius: var(--border-radius);
            background: white;
            box-shadow: var(--shadow-md);
            transition: var(--transition);
            border: 2px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .status-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--border-color);
            transition: var(--transition);
        }

        .status-item.active {
            background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
            border-color: var(--success-color);
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .status-item.active::before {
            background: var(--success-color);
        }

        .status-item.inactive {
            background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
            border-color: var(--danger-color);
        }

        .status-item.inactive::before {
            background: var(--danger-color);
        }

        .status-item h3 {
            font-size: 1.1em;
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--text-primary);
        }

        .status-item p {
            font-size: 0.9em;
            color: var(--text-secondary);
            font-weight: 500;
        }
        
        .main-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
            padding: 30px;
        }

        .activity-section {
            grid-column: 1 / -1;
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
            margin-top: 30px;
        }
        
        .control-panel, .command-panel, .activity-panel, .stats-panel {
            background: white;
            padding: 30px;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .control-panel:hover, .command-panel:hover, .activity-panel:hover, .stats-panel:hover {
            box-shadow: var(--shadow-lg);
            transform: translateY(-2px);
        }
        
        .control-panel h2, .command-panel h2, .activity-panel h2, .stats-panel h2 {
            margin-bottom: 25px;
            color: var(--text-primary);
            font-weight: 700;
            font-size: 1.5em;
            display: flex;
            align-items: center;
            gap: 10px;
            position: relative;
        }

        .control-panel h2::after, .command-panel h2::after, .activity-panel h2::after, .stats-panel h2::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 0;
            width: 50px;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            border-radius: 2px;
        }
        
        .control-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .btn {
            padding: 14px 24px;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: var(--transition);
            text-transform: none;
            position: relative;
            overflow: hidden;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            font-family: inherit;
            letter-spacing: 0.025em;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            box-shadow: var(--shadow-md);
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .btn-success {
            background: linear-gradient(135deg, var(--success-color), #059669);
            color: white;
            box-shadow: var(--shadow-md);
        }

        .btn-success:hover {
            background: linear-gradient(135deg, #059669, var(--success-color));
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .btn-danger {
            background: linear-gradient(135deg, var(--danger-color), #dc2626);
            color: white;
            box-shadow: var(--shadow-md);
        }

        .btn-danger:hover {
            background: linear-gradient(135deg, #dc2626, var(--danger-color));
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .btn-warning {
            background: linear-gradient(135deg, var(--warning-color), #d97706);
            color: white;
            box-shadow: var(--shadow-md);
        }

        .btn-warning:hover {
            background: linear-gradient(135deg, #d97706, var(--warning-color));
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }
        
        .command-list {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            background: white;
        }
        
        .command-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            border-bottom: 1px solid #eee;
            margin-bottom: 10px;
            border-radius: 5px;
            background: #f8f9fa;
        }
        
        .command-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        
        .test-results {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            background: white;
            border: 1px solid #dee2e6;
        }
        
        .alert {
            padding: 12px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .activity-feed {
            max-height: 500px;
            overflow-y: auto;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            background: var(--light-color);
            scrollbar-width: thin;
            scrollbar-color: var(--primary-color) var(--light-color);
        }

        .activity-feed::-webkit-scrollbar {
            width: 6px;
        }

        .activity-feed::-webkit-scrollbar-track {
            background: var(--light-color);
            border-radius: 3px;
        }

        .activity-feed::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: 3px;
        }

        .activity-feed::-webkit-scrollbar-thumb:hover {
            background: var(--primary-dark);
        }

        .activity-item {
            padding: 20px;
            margin: 8px;
            border-radius: var(--border-radius);
            background: white;
            border: 1px solid var(--border-color);
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .activity-item:hover {
            background-color: var(--light-color);
            transform: translateX(4px);
            box-shadow: var(--shadow-md);
        }

        .activity-item.new-activity {
            background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
            border-color: var(--success-color);
            animation: slideInRight 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(100px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .activity-item.new-activity {
            animation: slideInRight 0.6s cubic-bezier(0.4, 0, 0.2, 1), pulse 2s ease-in-out;
        }

        .activity-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .activity-type {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }

        .activity-type.gesture {
            background: #e3f2fd;
            color: #1976d2;
        }

        .activity-type.voice {
            background: #f3e5f5;
            color: #7b1fa2;
        }

        .activity-type.system {
            background: #e8f5e8;
            color: #388e3c;
        }

        .activity-time {
            font-size: 12px;
            color: #666;
        }

        .activity-action {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }

        .activity-details {
            font-size: 14px;
            color: #666;
        }

        .activity-status {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .activity-status.success {
            background: #28a745;
        }

        .activity-status.failed {
            background: #dc3545;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .stat-item {
            text-align: center;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }

        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #4facfe;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 12px;
            color: #666;
            text-transform: uppercase;
        }

        .activity-controls {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }

        .filter-btn {
            padding: 8px 16px;
            border: 1px solid #dee2e6;
            background: white;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .filter-btn:hover {
            background: #f8f9fa;
        }

        .filter-btn.active {
            background: #4facfe;
            color: white;
            border-color: #4facfe;
        }

        .search-box {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #dee2e6;
            border-radius: 20px;
            font-size: 14px;
        }

        .image-gallery {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 15px;
            max-height: 400px;
            overflow-y: auto;
            padding: 10px;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            background: var(--light-color);
        }

        .image-item {
            position: relative;
            border-radius: var(--border-radius);
            overflow: hidden;
            background: white;
            box-shadow: var(--shadow-sm);
            transition: var(--transition);
            cursor: pointer;
        }

        .image-item:hover {
            transform: scale(1.05);
            box-shadow: var(--shadow-md);
        }

        .image-item img {
            width: 100%;
            height: 120px;
            object-fit: cover;
            display: block;
        }

        .image-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0,0,0,0.8));
            color: white;
            padding: 8px;
            font-size: 12px;
            text-align: center;
        }

        .analytics-container {
            background: white;
            padding: 20px;
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            margin-bottom: 20px;
        }

        .image-modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.9);
            backdrop-filter: blur(5px);
        }

        .image-modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            max-width: 90%;
            max-height: 90%;
        }

        .image-modal img {
            width: 100%;
            height: auto;
            border-radius: var(--border-radius);
        }

        .image-modal-close {
            position: absolute;
            top: 20px;
            right: 30px;
            color: white;
            font-size: 40px;
            font-weight: bold;
            cursor: pointer;
            z-index: 1001;
        }

        .image-modal-close:hover {
            opacity: 0.7;
        }
        
        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }

            .activity-section {
                grid-template-columns: 1fr;
            }

            .status-bar {
                flex-direction: column;
                gap: 10px;
            }

            .control-buttons {
                grid-template-columns: 1fr;
            }

            .stats-grid {
                grid-template-columns: 1fr 1fr;
            }

            .activity-controls {
                flex-direction: column;
                gap: 10px;
            }

            .filter-btn {
                flex: 1;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎤 Voice & Gesture Control</h1>
            <p>Hands-free laptop control system</p>
        </div>
        
        <div class="status-bar">
            <div class="status-item" id="system-status">
                <h3>System</h3>
                <p id="system-text">Loading...</p>
            </div>
            <div class="status-item" id="voice-status">
                <h3>Voice Control</h3>
                <p id="voice-text">Loading...</p>
            </div>
            <div class="status-item" id="gesture-status">
                <h3>Gesture Control</h3>
                <p id="gesture-text">Loading...</p>
            </div>
        </div>
        
        <div class="main-content">
            <div class="control-panel">
                <h2>🎛️ System Controls</h2>
                
                <div class="control-buttons">
                    <button class="btn btn-success" onclick="controlSystem('start')">Start System</button>
                    <button class="btn btn-danger" onclick="controlSystem('stop')">Stop System</button>
                    <button class="btn btn-primary" onclick="controlSystem('toggle_voice')">Toggle Voice</button>
                    <button class="btn btn-primary" onclick="controlSystem('toggle_gesture')">Toggle Gesture</button>
                </div>
                
                <h3>🧪 Component Tests</h3>
                <div class="control-buttons">
                    <button class="btn btn-warning" onclick="testComponent('microphone')">Test Microphone</button>
                    <button class="btn btn-warning" onclick="testComponent('camera')">Test Camera</button>
                </div>
                
                <div class="test-results" id="test-results" style="display: none;">
                    <h4>Test Results:</h4>
                    <div id="test-output"></div>
                </div>
            </div>
            
            <div class="command-panel">
                <h2>📋 Available Commands</h2>
                
                <h3>Voice Commands</h3>
                <div class="command-list" id="voice-commands">
                    Loading commands...
                </div>
                
                <h3>Gesture Commands</h3>
                <div class="command-list" id="gesture-commands">
                    Loading commands...
                </div>
            </div>
        </div>

        <div class="activity-section">
            <div class="activity-panel">
                <h2>📊 Real-time Activity Feed</h2>

                <div class="activity-controls">
                    <button class="filter-btn active" data-filter="all">All</button>
                    <button class="filter-btn" data-filter="gesture">Gestures</button>
                    <button class="filter-btn" data-filter="voice">Voice</button>
                    <button class="filter-btn" data-filter="system">System</button>
                    <select class="filter-btn" id="time-filter" style="border-radius: 20px; padding: 8px 12px;">
                        <option value="all">All Time</option>
                        <option value="5">Last 5 minutes</option>
                        <option value="30">Last 30 minutes</option>
                        <option value="60">Last hour</option>
                        <option value="1440">Last 24 hours</option>
                    </select>
                    <input type="text" class="search-box" placeholder="Search activities..." id="activity-search">
                </div>

                <div class="activity-feed" id="activity-feed">
                    <div style="text-align: center; padding: 40px; color: #666;">
                        <p>No activities yet. Start using voice commands or gestures!</p>
                    </div>
                </div>
            </div>

            <div class="stats-panel">
                <h2>📈 Activity Statistics</h2>

                <div class="stats-grid" id="stats-grid">
                    <div class="stat-item">
                        <div class="stat-value" id="total-activities">0</div>
                        <div class="stat-label">Total Activities</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="success-rate">0%</div>
                        <div class="stat-label">Success Rate</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="gesture-count">0</div>
                        <div class="stat-label">Gestures</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="voice-count">0</div>
                        <div class="stat-label">Voice Commands</div>
                    </div>
                </div>

                <div style="margin-top: 20px;">
                    <button class="btn btn-primary" onclick="exportActivities()" style="width: 100%; margin-bottom: 10px;">
                        Export Activities
                    </button>
                    <button class="btn btn-warning" onclick="clearActivities()" style="width: 100%;">
                        Clear Activity History
                    </button>
                </div>
            </div>
        </div>

        <div class="activity-section">
            <div class="activity-panel">
                <h2>📸 Image Capture Gallery</h2>

                <div class="image-controls" style="margin-bottom: 20px;">
                    <button class="btn btn-primary" onclick="loadImages()">🔄 Refresh Images</button>
                    <button class="btn btn-warning" onclick="clearImages()">🗑️ Clear Images</button>
                    <span id="image-stats" style="margin-left: 20px; color: var(--text-secondary);"></span>
                </div>

                <div class="image-gallery" id="image-gallery">
                    <div style="text-align: center; padding: 40px; color: #666;">
                        <p>Loading images...</p>
                    </div>
                </div>
            </div>

            <div class="stats-panel">
                <h2>📊 Advanced Analytics</h2>

                <div class="analytics-container">
                    <canvas id="activityChart" width="400" height="200"></canvas>
                </div>

                <div class="stats-grid" style="margin-top: 20px;">
                    <div class="stat-item">
                        <div class="stat-value" id="avg-response-time">0ms</div>
                        <div class="stat-label">Avg Response Time</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="images-captured">0</div>
                        <div class="stat-label">Images Captured</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Image Modal -->
    <div id="imageModal" class="image-modal">
        <span class="image-modal-close" onclick="closeImageModal()">&times;</span>
        <div class="image-modal-content">
            <img id="modalImage" src="" alt="Captured Image">
        </div>
    </div>

    <script>
        // Initialize Socket.IO connection
        const socket = io();
        
        // Socket event handlers
        socket.on('connect', function() {
            console.log('Connected to server');
            loadCommands();
        });
        
        socket.on('status_update', function(data) {
            updateStatus(data);
        });
        
        socket.on('status', function(data) {
            updateStatus(data);
        });

        socket.on('new_activity', function(activity) {
            addActivityToFeed(activity);
        });

        socket.on('activity_stats_update', function(stats) {
            updateActivityStats(stats);
        });

        socket.on('activities', function(activities) {
            displayActivities(activities);
        });
        
        // Update status display
        function updateStatus(data) {
            if (data.system) {
                const systemStatus = document.getElementById('system-status');
                const systemText = document.getElementById('system-text');
                
                if (data.system.running) {
                    systemStatus.className = 'status-item active';
                    systemText.textContent = 'Running';
                } else {
                    systemStatus.className = 'status-item inactive';
                    systemText.textContent = 'Stopped';
                }
                
                // Voice status
                const voiceStatus = document.getElementById('voice-status');
                const voiceText = document.getElementById('voice-text');
                
                if (data.system.voice_enabled) {
                    voiceStatus.className = 'status-item active';
                    voiceText.textContent = 'Enabled';
                } else {
                    voiceStatus.className = 'status-item inactive';
                    voiceText.textContent = 'Disabled';
                }
                
                // Gesture status
                const gestureStatus = document.getElementById('gesture-status');
                const gestureText = document.getElementById('gesture-text');
                
                if (data.system.gesture_enabled) {
                    gestureStatus.className = 'status-item active';
                    gestureText.textContent = 'Enabled';
                } else {
                    gestureStatus.className = 'status-item inactive';
                    gestureText.textContent = 'Disabled';
                }
            }
        }
        
        // Control system functions
        function controlSystem(action) {
            fetch(`/api/control/${action}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert(data.message, 'success');
                } else {
                    showAlert(data.error || 'Operation failed', 'danger');
                }
            })
            .catch(error => {
                showAlert('Network error: ' + error.message, 'danger');
            });
        }
        
        // Test components
        function testComponent(component) {
            const testResults = document.getElementById('test-results');
            const testOutput = document.getElementById('test-output');
            
            testResults.style.display = 'block';
            testOutput.innerHTML = '<div class="alert alert-info">Testing ' + component + '...</div>';
            
            fetch(`/api/test/${component}`)
            .then(response => response.json())
            .then(data => {
                const alertClass = data.success ? 'alert-success' : 'alert-danger';
                testOutput.innerHTML = `<div class="alert ${alertClass}">${data.message}</div>`;
            })
            .catch(error => {
                testOutput.innerHTML = '<div class="alert alert-danger">Test failed: ' + error.message + '</div>';
            });
        }
        
        // Load available commands
        function loadCommands() {
            fetch('/api/commands')
            .then(response => response.json())
            .then(data => {
                displayCommands('voice-commands', data.voice || []);
                displayCommands('gesture-commands', data.gesture || []);
            })
            .catch(error => {
                console.error('Error loading commands:', error);
            });
        }
        
        // Display commands in the UI
        function displayCommands(containerId, commands) {
            const container = document.getElementById(containerId);
            
            if (commands.length === 0) {
                container.innerHTML = '<p>No commands available</p>';
                return;
            }
            
            container.innerHTML = commands.map(command => 
                `<div class="command-item">
                    <span><strong>${command}</strong></span>
                </div>`
            ).join('');
        }
        
        // Show alert messages
        function showAlert(message, type) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type}`;
            alertDiv.textContent = message;
            
            document.body.appendChild(alertDiv);
            
            setTimeout(() => {
                alertDiv.remove();
            }, 5000);
        }
        
        // Activity management functions
        let currentFilter = 'all';
        let allActivities = [];

        function addActivityToFeed(activity) {
            allActivities.unshift(activity); // Add to beginning

            // Keep only last 100 activities in memory
            if (allActivities.length > 100) {
                allActivities = allActivities.slice(0, 100);
            }

            // Apply current filter and display
            displayFilteredActivities();

            // Update chart and advanced stats
            if (activityChart) {
                updateChart();
            }
            updateAdvancedStats();
        }

        function displayActivities(activities) {
            allActivities = activities;
            displayFilteredActivities();
        }

        function displayFilteredActivities() {
            const feed = document.getElementById('activity-feed');
            const searchTerm = document.getElementById('activity-search').value.toLowerCase();
            const timeFilter = document.getElementById('time-filter').value;

            let filteredActivities = allActivities;

            // Apply type filter
            if (currentFilter !== 'all') {
                filteredActivities = filteredActivities.filter(activity =>
                    activity.type === currentFilter
                );
            }

            // Apply time filter
            if (timeFilter !== 'all') {
                const minutesAgo = parseInt(timeFilter);
                const cutoffTime = Date.now() / 1000 - (minutesAgo * 60);
                filteredActivities = filteredActivities.filter(activity =>
                    activity.timestamp >= cutoffTime
                );
            }

            // Apply search filter
            if (searchTerm) {
                filteredActivities = filteredActivities.filter(activity =>
                    activity.action.toLowerCase().includes(searchTerm) ||
                    JSON.stringify(activity.details).toLowerCase().includes(searchTerm)
                );
            }

            if (filteredActivities.length === 0) {
                feed.innerHTML = `
                    <div style="text-align: center; padding: 40px; color: #666;">
                        <p>No activities found matching your criteria.</p>
                    </div>
                `;
                return;
            }

            feed.innerHTML = filteredActivities.map(activity => createActivityHTML(activity)).join('');
        }

        function createActivityHTML(activity) {
            const statusClass = activity.success ? 'success' : 'failed';
            const typeClass = activity.type;

            return `
                <div class="activity-item" data-id="${activity.id}">
                    <div class="activity-header">
                        <div>
                            <span class="activity-type ${typeClass}">${activity.type}</span>
                            <span class="activity-status ${statusClass}"></span>
                        </div>
                        <div class="activity-time">${activity.time_ago}</div>
                    </div>
                    <div class="activity-action">${activity.action}</div>
                    <div class="activity-details">
                        ${formatActivityDetails(activity)}
                    </div>
                </div>
            `;
        }

        function formatActivityDetails(activity) {
            let details = [];

            if (activity.details.action && activity.details.action !== activity.action) {
                details.push(`Action: ${activity.details.action}`);
            }

            if (activity.details.gesture) {
                details.push(`Gesture: ${activity.details.gesture}`);
            }

            if (activity.details.command) {
                details.push(`Command: "${activity.details.command}"`);
            }

            if (activity.duration) {
                details.push(`Duration: ${(activity.duration * 1000).toFixed(0)}ms`);
            }

            return details.join(' • ') || 'No additional details';
        }

        function updateActivityStats(stats) {
            document.getElementById('total-activities').textContent = stats.total_activities || 0;
            document.getElementById('success-rate').textContent = `${(stats.success_rate || 0).toFixed(1)}%`;
            document.getElementById('gesture-count').textContent = stats.gesture_count || 0;
            document.getElementById('voice-count').textContent = stats.voice_count || 0;
        }

        function exportActivities() {
            if (allActivities.length === 0) {
                showAlert('No activities to export', 'info');
                return;
            }

            const csvContent = generateCSV(allActivities);
            const blob = new Blob([csvContent], { type: 'text/csv' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `activities_${new Date().toISOString().split('T')[0]}.csv`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);

            showAlert('Activities exported successfully', 'success');
        }

        function generateCSV(activities) {
            const headers = ['Timestamp', 'Type', 'Action', 'Success', 'Duration (ms)', 'Details'];
            const rows = activities.map(activity => [
                new Date(activity.timestamp * 1000).toISOString(),
                activity.type,
                activity.action,
                activity.success ? 'Yes' : 'No',
                activity.duration ? (activity.duration * 1000).toFixed(0) : '',
                JSON.stringify(activity.details).replace(/"/g, '""')
            ]);

            const csvContent = [headers, ...rows]
                .map(row => row.map(field => `"${field}"`).join(','))
                .join('\n');

            return csvContent;
        }

        function clearActivities() {
            if (confirm('Are you sure you want to clear all activity history?')) {
                fetch('/api/activities/clear', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        allActivities = [];
                        displayFilteredActivities();
                        updateActivityStats({
                            total_activities: 0,
                            success_rate: 0,
                            gesture_count: 0,
                            voice_count: 0
                        });
                        showAlert('Activity history cleared', 'success');
                    } else {
                        showAlert(data.error || 'Failed to clear activities', 'danger');
                    }
                })
                .catch(error => {
                    showAlert('Network error: ' + error.message, 'danger');
                });
            }
        }

        function loadInitialActivities() {
            // Load recent activities
            socket.emit('request_activities', { limit: 50 });

            // Load activity statistics
            socket.emit('request_activity_stats');
        }

        // Setup filter buttons
        document.addEventListener('DOMContentLoaded', function() {
            // Filter button event listeners
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    // Update active state
                    document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
                    this.classList.add('active');

                    // Update filter
                    currentFilter = this.dataset.filter;
                    displayFilteredActivities();
                });
            });

            // Search box event listener
            document.getElementById('activity-search').addEventListener('input', function() {
                displayFilteredActivities();
            });

            // Time filter event listener
            document.getElementById('time-filter').addEventListener('change', function() {
                displayFilteredActivities();
            });

            // Load initial data
            loadCommands();
            loadInitialActivities();
            loadImages();
            initializeChart();
        });

        // Image management functions
        let activityChart = null;

        function loadImages() {
            fetch('/api/images?count=20')
            .then(response => response.json())
            .then(images => {
                displayImages(images);
                updateImageStats();
            })
            .catch(error => {
                console.error('Error loading images:', error);
                document.getElementById('image-gallery').innerHTML =
                    '<div style="text-align: center; padding: 40px; color: #666;"><p>Error loading images</p></div>';
            });
        }

        function displayImages(images) {
            const gallery = document.getElementById('image-gallery');

            if (images.length === 0) {
                gallery.innerHTML =
                    '<div style="text-align: center; padding: 40px; color: #666;"><p>No images captured yet</p></div>';
                return;
            }

            gallery.innerHTML = images.map(image => `
                <div class="image-item" onclick="openImageModal('${image.url}')">
                    <img src="${image.url}" alt="Captured at ${image.created}" loading="lazy">
                    <div class="image-overlay">
                        ${new Date(image.created).toLocaleTimeString()}
                    </div>
                </div>
            `).join('');
        }

        function updateImageStats() {
            fetch('/api/images/stats')
            .then(response => response.json())
            .then(stats => {
                document.getElementById('image-stats').textContent =
                    `${stats.total_images} images (${stats.total_size_mb} MB)`;
                document.getElementById('images-captured').textContent = stats.total_images;
            })
            .catch(error => {
                console.error('Error loading image stats:', error);
            });
        }

        function clearImages() {
            if (confirm('Are you sure you want to clear all captured images?')) {
                fetch('/api/images/clear', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        loadImages();
                        showAlert(data.message, 'success');
                    } else {
                        showAlert(data.error || 'Failed to clear images', 'danger');
                    }
                })
                .catch(error => {
                    showAlert('Network error: ' + error.message, 'danger');
                });
            }
        }

        function openImageModal(imageUrl) {
            document.getElementById('modalImage').src = imageUrl;
            document.getElementById('imageModal').style.display = 'block';
        }

        function closeImageModal() {
            document.getElementById('imageModal').style.display = 'none';
        }

        // Close modal when clicking outside the image
        window.onclick = function(event) {
            const modal = document.getElementById('imageModal');
            if (event.target === modal) {
                closeImageModal();
            }
        }

        // Analytics functions
        function initializeChart() {
            const ctx = document.getElementById('activityChart').getContext('2d');
            activityChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'Gestures',
                        data: [],
                        borderColor: 'rgb(99, 102, 241)',
                        backgroundColor: 'rgba(99, 102, 241, 0.1)',
                        tension: 0.4
                    }, {
                        label: 'Voice Commands',
                        data: [],
                        borderColor: 'rgb(139, 92, 246)',
                        backgroundColor: 'rgba(139, 92, 246, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: 'Activity Trends (Last 24 Hours)'
                        }
                    }
                }
            });

            updateChart();
        }

        function updateChart() {
            // Generate hourly activity data for the last 24 hours
            const now = new Date();
            const labels = [];
            const gestureData = [];
            const voiceData = [];

            for (let i = 23; i >= 0; i--) {
                const hour = new Date(now.getTime() - (i * 60 * 60 * 1000));
                labels.push(hour.getHours() + ':00');

                // Count activities for this hour
                const hourStart = hour.getTime() / 1000;
                const hourEnd = hourStart + 3600;

                const gestureCount = allActivities.filter(activity =>
                    activity.type === 'gesture' &&
                    activity.timestamp >= hourStart &&
                    activity.timestamp < hourEnd
                ).length;

                const voiceCount = allActivities.filter(activity =>
                    activity.type === 'voice' &&
                    activity.timestamp >= hourStart &&
                    activity.timestamp < hourEnd
                ).length;

                gestureData.push(gestureCount);
                voiceData.push(voiceCount);
            }

            activityChart.data.labels = labels;
            activityChart.data.datasets[0].data = gestureData;
            activityChart.data.datasets[1].data = voiceData;
            activityChart.update();
        }

        function updateAdvancedStats() {
            // Calculate average response time
            const responseTimes = allActivities
                .filter(activity => activity.duration)
                .map(activity => activity.duration * 1000);

            const avgResponseTime = responseTimes.length > 0
                ? Math.round(responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length)
                : 0;

            document.getElementById('avg-response-time').textContent = avgResponseTime + 'ms';
        }
    </script>
</body>
</html>
