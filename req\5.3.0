Requirement already satisfied: flask-socketio in c:\users\<USER>\appdata\local\programs\python\python312\lib\site-packages (5.3.6)
Requirement already satisfied: Flask>=0.9 in c:\users\<USER>\appdata\local\programs\python\python312\lib\site-packages (from flask-socketio) (3.0.2)
Requirement already satisfied: python-socketio>=5.0.2 in c:\users\<USER>\appdata\local\programs\python\python312\lib\site-packages (from flask-socketio) (5.13.0)
Requirement already satisfied: Werkzeug>=3.0.0 in c:\users\<USER>\appdata\local\programs\python\python312\lib\site-packages (from Flask>=0.9->flask-socketio) (3.1.3)
Requirement already satisfied: Jinja2>=3.1.2 in c:\users\<USER>\appdata\local\programs\python\python312\lib\site-packages (from Flask>=0.9->flask-socketio) (3.1.6)
Requirement already satisfied: itsdangerous>=2.1.2 in c:\users\<USER>\appdata\local\programs\python\python312\lib\site-packages (from Flask>=0.9->flask-socketio) (2.2.0)
Requirement already satisfied: click>=8.1.3 in c:\users\<USER>\appdata\local\programs\python\python312\lib\site-packages (from Flask>=0.9->flask-socketio) (8.1.8)
Requirement already satisfied: blinker>=1.6.2 in c:\users\<USER>\appdata\local\programs\python\python312\lib\site-packages (from Flask>=0.9->flask-socketio) (1.9.0)
Requirement already satisfied: colorama in c:\users\<USER>\appdata\local\programs\python\python312\lib\site-packages (from click>=8.1.3->Flask>=0.9->flask-socketio) (0.4.6)
Requirement already satisfied: MarkupSafe>=2.0 in c:\users\<USER>\appdata\local\programs\python\python312\lib\site-packages (from Jinja2>=3.1.2->Flask>=0.9->flask-socketio) (3.0.2)
Requirement already satisfied: bidict>=0.21.0 in c:\users\<USER>\appdata\local\programs\python\python312\lib\site-packages (from python-socketio>=5.0.2->flask-socketio) (0.23.1)
Requirement already satisfied: python-engineio>=4.11.0 in c:\users\<USER>\appdata\local\programs\python\python312\lib\site-packages (from python-socketio>=5.0.2->flask-socketio) (4.12.1)
Requirement already satisfied: simple-websocket>=0.10.0 in c:\users\<USER>\appdata\local\programs\python\python312\lib\site-packages (from python-engineio>=4.11.0->python-socketio>=5.0.2->flask-socketio) (1.1.0)
Requirement already satisfied: wsproto in c:\users\<USER>\appdata\local\programs\python\python312\lib\site-packages (from simple-websocket>=0.10.0->python-engineio>=4.11.0->python-socketio>=5.0.2->flask-socketio) (1.2.0)
Requirement already satisfied: h11<1,>=0.9.0 in c:\users\<USER>\appdata\local\programs\python\python312\lib\site-packages (from wsproto->simple-websocket>=0.10.0->python-engineio>=4.11.0->python-socketio>=5.0.2->flask-socketio) (0.16.0)
