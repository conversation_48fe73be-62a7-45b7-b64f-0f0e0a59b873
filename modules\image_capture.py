"""
Image Capture Module
Handles automatic image capture with gesture detection overlay
"""

import cv2
import logging
import threading
import time
import os
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any
import numpy as np

class ImageCapture:
    """Handles automatic image capture with gesture detection overlay"""
    
    def __init__(self, config: Dict[str, Any], activity_manager=None):
        self.config = config
        self.activity_manager = activity_manager
        self.logger = logging.getLogger(__name__)
        
        # Image capture configuration
        self.capture_config = config.get('image_capture', {})
        self.enabled = self.capture_config.get('enabled', True)
        self.interval = self.capture_config.get('interval', 3)  # seconds
        self.storage_dir = Path(self.capture_config.get('storage_dir', 'data/images'))
        self.max_images = self.capture_config.get('max_images', 1000)
        self.image_quality = self.capture_config.get('quality', 85)
        
        # Create storage directory
        self.storage_dir.mkdir(parents=True, exist_ok=True)
        
        # Capture state
        self.running = False
        self.capture_thread = None
        self.last_capture_time = 0
        
        # Camera reference (will be set by gesture controller)
        self.camera = None
        self.current_gesture = None
        self.gesture_landmarks = None
        
        self.logger.info(f"Image Capture initialized - Storage: {self.storage_dir}")
    
    def set_camera(self, camera):
        """Set camera reference from gesture controller"""
        self.camera = camera
    
    def set_current_gesture(self, gesture: str, landmarks=None):
        """Update current gesture for overlay"""
        self.current_gesture = gesture
        self.gesture_landmarks = landmarks
    
    def start(self):
        """Start automatic image capture"""
        if not self.enabled or self.running:
            return
        
        self.running = True
        self.capture_thread = threading.Thread(target=self._capture_loop, daemon=True)
        self.capture_thread.start()
        self.logger.info("Image capture started")
    
    def stop(self):
        """Stop automatic image capture"""
        self.running = False
        if self.capture_thread:
            self.capture_thread.join(timeout=1)
        self.logger.info("Image capture stopped")
    
    def _capture_loop(self):
        """Main capture loop"""
        while self.running:
            try:
                current_time = time.time()
                
                # Check if it's time to capture
                if current_time - self.last_capture_time >= self.interval:
                    self._capture_image()
                    self.last_capture_time = current_time
                
                time.sleep(0.1)  # Small sleep to prevent high CPU usage
                
            except Exception as e:
                self.logger.error(f"Error in capture loop: {e}")
                time.sleep(1)
    
    def _capture_image(self):
        """Capture and save image with overlay"""
        if not self.camera or not self.camera.isOpened():
            return
        
        try:
            # Read frame from camera
            ret, frame = self.camera.read()
            if not ret:
                return
            
            # Flip frame horizontally for mirror effect
            frame = cv2.flip(frame, 1)
            
            # Add overlay information
            overlay_frame = self._add_overlay(frame.copy())
            
            # Generate filename
            timestamp = datetime.now()
            filename = f"capture_{timestamp.strftime('%Y%m%d_%H%M%S')}.jpg"
            filepath = self.storage_dir / filename
            
            # Save image
            cv2.imwrite(str(filepath), overlay_frame, [cv2.IMWRITE_JPEG_QUALITY, self.image_quality])
            
            # Track capture activity
            if self.activity_manager:
                details = {
                    'filename': filename,
                    'timestamp': timestamp.isoformat(),
                    'gesture_detected': self.current_gesture,
                    'image_size': f"{frame.shape[1]}x{frame.shape[0]}",
                    'file_size': filepath.stat().st_size if filepath.exists() else 0
                }
                self.activity_manager.add_activity(
                    activity_type='system',
                    action='image_captured',
                    details=details,
                    success=True
                )
            
            # Clean up old images if necessary
            self._cleanup_old_images()
            
            self.logger.debug(f"Image captured: {filename}")
            
        except Exception as e:
            self.logger.error(f"Error capturing image: {e}")
    
    def _add_overlay(self, frame):
        """Add gesture detection overlay to frame"""
        try:
            # Add timestamp
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            cv2.putText(frame, timestamp, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            cv2.putText(frame, timestamp, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 1)
            
            # Add gesture information
            if self.current_gesture:
                gesture_text = f"Gesture: {self.current_gesture.upper()}"
                cv2.putText(frame, gesture_text, (10, 70), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)
                
                # Add gesture indicator box
                cv2.rectangle(frame, (10, 80), (200, 120), (0, 255, 0), 2)
                cv2.putText(frame, "GESTURE DETECTED", (15, 105), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
            else:
                cv2.putText(frame, "No gesture", (10, 70), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (128, 128, 128), 2)
            
            # Add system status
            status_text = "RECORDING"
            cv2.putText(frame, status_text, (frame.shape[1] - 150, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
            
            # Add recording indicator (red circle)
            cv2.circle(frame, (frame.shape[1] - 30, 25), 8, (0, 0, 255), -1)
            
            return frame
            
        except Exception as e:
            self.logger.error(f"Error adding overlay: {e}")
            return frame
    
    def _cleanup_old_images(self):
        """Remove old images if exceeding max count"""
        try:
            image_files = list(self.storage_dir.glob("capture_*.jpg"))
            
            if len(image_files) > self.max_images:
                # Sort by creation time and remove oldest
                image_files.sort(key=lambda x: x.stat().st_ctime)
                files_to_remove = image_files[:-self.max_images]
                
                for file_path in files_to_remove:
                    file_path.unlink()
                    self.logger.debug(f"Removed old image: {file_path.name}")
                
        except Exception as e:
            self.logger.error(f"Error cleaning up old images: {e}")
    
    def get_recent_images(self, count: int = 10):
        """Get list of recent captured images"""
        try:
            image_files = list(self.storage_dir.glob("capture_*.jpg"))
            image_files.sort(key=lambda x: x.stat().st_ctime, reverse=True)
            
            recent_images = []
            for img_file in image_files[:count]:
                stat = img_file.stat()
                recent_images.append({
                    'filename': img_file.name,
                    'path': str(img_file),
                    'size': stat.st_size,
                    'created': datetime.fromtimestamp(stat.st_ctime).isoformat(),
                    'url': f"/api/images/{img_file.name}"
                })
            
            return recent_images
            
        except Exception as e:
            self.logger.error(f"Error getting recent images: {e}")
            return []
    
    def get_image_stats(self):
        """Get image capture statistics"""
        try:
            image_files = list(self.storage_dir.glob("capture_*.jpg"))
            total_size = sum(f.stat().st_size for f in image_files)
            
            return {
                'total_images': len(image_files),
                'total_size_mb': round(total_size / (1024 * 1024), 2),
                'storage_dir': str(self.storage_dir),
                'enabled': self.enabled,
                'interval': self.interval,
                'max_images': self.max_images
            }
            
        except Exception as e:
            self.logger.error(f"Error getting image stats: {e}")
            return {
                'total_images': 0,
                'total_size_mb': 0,
                'storage_dir': str(self.storage_dir),
                'enabled': self.enabled,
                'interval': self.interval,
                'max_images': self.max_images
            }
    
    def clear_images(self):
        """Clear all captured images"""
        try:
            image_files = list(self.storage_dir.glob("capture_*.jpg"))
            for img_file in image_files:
                img_file.unlink()
            
            self.logger.info(f"Cleared {len(image_files)} images")
            return len(image_files)
            
        except Exception as e:
            self.logger.error(f"Error clearing images: {e}")
            return 0
