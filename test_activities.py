#!/usr/bin/env python3
"""
Test script to generate sample activities for testing the activity display system
"""

import time
import requests
import json

def test_activity_display():
    """Test the activity display system by simulating activities"""
    
    base_url = "http://127.0.0.1:5000"
    
    print("Testing Activity Display System...")
    print("=" * 50)
    
    # Test 1: Check if the web interface is running
    try:
        response = requests.get(f"{base_url}/api/status")
        if response.status_code == 200:
            print("✓ Web interface is running")
            status = response.json()
            print(f"  System running: {status.get('system', {}).get('running', False)}")
        else:
            print("✗ Web interface not accessible")
            return
    except Exception as e:
        print(f"✗ Error connecting to web interface: {e}")
        return
    
    # Test 2: Check activity endpoints
    try:
        response = requests.get(f"{base_url}/api/activities")
        if response.status_code == 200:
            print("✓ Activity API endpoint is working")
            activities = response.json()
            print(f"  Current activities count: {len(activities)}")
        else:
            print("✗ Activity API endpoint not working")
    except Exception as e:
        print(f"✗ Error accessing activity API: {e}")
    
    # Test 3: Check activity statistics
    try:
        response = requests.get(f"{base_url}/api/activities/stats")
        if response.status_code == 200:
            print("✓ Activity statistics API is working")
            stats = response.json()
            print(f"  Total activities: {stats.get('total_activities', 0)}")
            print(f"  Success rate: {stats.get('success_rate', 0):.1f}%")
            print(f"  Gesture count: {stats.get('gesture_count', 0)}")
            print(f"  Voice count: {stats.get('voice_count', 0)}")
        else:
            print("✗ Activity statistics API not working")
    except Exception as e:
        print(f"✗ Error accessing activity statistics: {e}")
    
    # Test 4: Test search functionality
    try:
        response = requests.get(f"{base_url}/api/activities/search?q=gesture")
        if response.status_code == 200:
            print("✓ Activity search API is working")
            search_results = response.json()
            print(f"  Search results for 'gesture': {len(search_results)} activities")
        else:
            print("✗ Activity search API not working")
    except Exception as e:
        print(f"✗ Error accessing activity search: {e}")
    
    # Test 5: Test recent activities
    try:
        response = requests.get(f"{base_url}/api/activities/recent?minutes=60")
        if response.status_code == 200:
            print("✓ Recent activities API is working")
            recent_activities = response.json()
            print(f"  Recent activities (last 60 min): {len(recent_activities)}")
        else:
            print("✗ Recent activities API not working")
    except Exception as e:
        print(f"✗ Error accessing recent activities: {e}")
    
    print("\n" + "=" * 50)
    print("Activity Display System Test Complete!")
    print("\nTo test real-time activity tracking:")
    print("1. Open the web interface at http://127.0.0.1:5000")
    print("2. Perform gestures in front of your camera (thumbs up, fist, etc.)")
    print("3. Say 'computer' followed by voice commands")
    print("4. Watch the activity feed update in real-time!")
    print("\nAvailable gestures: thumbs_up, thumbs_down, fist, open_palm, peace")
    print("Available voice commands: Check the dashboard for the full list")

if __name__ == "__main__":
    test_activity_display()
