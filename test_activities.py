#!/usr/bin/env python3
"""
Test script to generate sample activities for testing the activity display system
"""

import time
import requests
import json

def test_enhanced_system():
    """Test the enhanced system with all new features"""

    base_url = "http://127.0.0.1:5000"

    print("Testing Enhanced Voice & Gesture Control System...")
    print("=" * 60)
    
    # Test 1: Check if the web interface is running
    try:
        response = requests.get(f"{base_url}/api/status")
        if response.status_code == 200:
            print("✓ Web interface is running")
            status = response.json()
            print(f"  System running: {status.get('system', {}).get('running', False)}")
        else:
            print("✗ Web interface not accessible")
            return
    except Exception as e:
        print(f"✗ Error connecting to web interface: {e}")
        return
    
    # Test 2: Check activity endpoints
    try:
        response = requests.get(f"{base_url}/api/activities")
        if response.status_code == 200:
            print("✓ Activity API endpoint is working")
            activities = response.json()
            print(f"  Current activities count: {len(activities)}")
        else:
            print("✗ Activity API endpoint not working")
    except Exception as e:
        print(f"✗ Error accessing activity API: {e}")
    
    # Test 3: Check activity statistics
    try:
        response = requests.get(f"{base_url}/api/activities/stats")
        if response.status_code == 200:
            print("✓ Activity statistics API is working")
            stats = response.json()
            print(f"  Total activities: {stats.get('total_activities', 0)}")
            print(f"  Success rate: {stats.get('success_rate', 0):.1f}%")
            print(f"  Gesture count: {stats.get('gesture_count', 0)}")
            print(f"  Voice count: {stats.get('voice_count', 0)}")
        else:
            print("✗ Activity statistics API not working")
    except Exception as e:
        print(f"✗ Error accessing activity statistics: {e}")
    
    # Test 4: Test search functionality
    try:
        response = requests.get(f"{base_url}/api/activities/search?q=gesture")
        if response.status_code == 200:
            print("✓ Activity search API is working")
            search_results = response.json()
            print(f"  Search results for 'gesture': {len(search_results)} activities")
        else:
            print("✗ Activity search API not working")
    except Exception as e:
        print(f"✗ Error accessing activity search: {e}")
    
    # Test 5: Test recent activities
    try:
        response = requests.get(f"{base_url}/api/activities/recent?minutes=60")
        if response.status_code == 200:
            print("✓ Recent activities API is working")
            recent_activities = response.json()
            print(f"  Recent activities (last 60 min): {len(recent_activities)}")
        else:
            print("✗ Recent activities API not working")
    except Exception as e:
        print(f"✗ Error accessing recent activities: {e}")
    
    # Test 6: Test image capture endpoints
    try:
        response = requests.get(f"{base_url}/api/images/stats")
        if response.status_code == 200:
            print("✓ Image capture statistics API is working")
            stats = response.json()
            print(f"  Total images: {stats.get('total_images', 0)}")
            print(f"  Storage size: {stats.get('total_size_mb', 0)} MB")
            print(f"  Capture enabled: {stats.get('enabled', False)}")
            print(f"  Capture interval: {stats.get('interval', 0)} seconds")
        else:
            print("✗ Image capture statistics API not working")
    except Exception as e:
        print(f"✗ Error accessing image capture stats: {e}")

    # Test 7: Test recent images endpoint
    try:
        response = requests.get(f"{base_url}/api/images?count=5")
        if response.status_code == 200:
            print("✓ Recent images API is working")
            images = response.json()
            print(f"  Recent images available: {len(images)}")
        else:
            print("✗ Recent images API not working")
    except Exception as e:
        print(f"✗ Error accessing recent images: {e}")

    print("\n" + "=" * 60)
    print("Enhanced System Test Complete!")
    print("\n🎯 NEW FEATURES AVAILABLE:")
    print("=" * 60)
    print("📱 ENHANCED DASHBOARD:")
    print("  • Modern UI with glassmorphism design")
    print("  • Real-time activity feed with animations")
    print("  • Advanced filtering and search")
    print("  • Responsive design for all devices")
    print("\n💾 JSON ACTIVITY STORAGE:")
    print("  • Persistent storage in data/activities.json")
    print("  • Automatic backups every save")
    print("  • Auto-save every 30 seconds")
    print("  • Activity history preserved between sessions")
    print("\n📸 IMAGE CAPTURE SYSTEM:")
    print("  • Automatic capture every 3 seconds")
    print("  • Gesture detection overlay")
    print("  • Image gallery with modal view")
    print("  • Storage management and cleanup")
    print("\n📊 ADVANCED ANALYTICS:")
    print("  • Real-time activity charts")
    print("  • Performance metrics")
    print("  • Trend analysis")
    print("  • Response time tracking")
    print("\n🚀 HOW TO TEST:")
    print("1. Open the web interface at http://127.0.0.1:5000")
    print("2. Perform gestures in front of your camera")
    print("3. Say 'computer' followed by voice commands")
    print("4. Watch real-time updates in all sections!")
    print("5. Check the image gallery for captured photos")
    print("6. View analytics charts and statistics")
    print("\n✨ Available gestures: thumbs_up, thumbs_down, fist, open_palm, peace")
    print("🎤 Voice commands: Check the dashboard for the full list")

if __name__ == "__main__":
    test_enhanced_system()
