#!/usr/bin/env python3
"""
Test script for Voice and Gesture Control System
Tests individual components and basic functionality
"""

import sys
import os
import time
import logging
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """Test if all required modules can be imported"""
    print("Testing imports...")
    
    try:
        import speech_recognition as sr
        print("✓ SpeechRecognition imported successfully")
    except ImportError as e:
        print(f"✗ SpeechRecognition import failed: {e}")
        return False
    
    try:
        import cv2
        print("✓ OpenCV imported successfully")
    except ImportError as e:
        print(f"✗ OpenCV import failed: {e}")
        return False
    
    try:
        import mediapipe as mp
        print("✓ MediaPipe imported successfully")
    except ImportError as e:
        print(f"✗ MediaPipe import failed: {e}")
        return False
    
    try:
        import pyautogui
        print("✓ PyAutoGUI imported successfully")
    except ImportError as e:
        print(f"✗ PyAutoGUI import failed: {e}")
        return False
    
    try:
        from PyQt5.QtWidgets import QApplication
        print("✓ PyQt5 imported successfully")
    except ImportError as e:
        print(f"✗ PyQt5 import failed: {e}")
        return False
    
    try:
        import flask
        print("✓ Flask imported successfully")
    except ImportError as e:
        print(f"✗ Flask import failed: {e}")
        return False
    
    return True

def test_project_modules():
    """Test if project modules can be imported"""
    print("\nTesting project modules...")
    
    try:
        from modules.command_executor import CommandExecutor
        print("✓ CommandExecutor imported successfully")
    except ImportError as e:
        print(f"✗ CommandExecutor import failed: {e}")
        return False
    
    try:
        from modules.voice_control import VoiceController
        print("✓ VoiceController imported successfully")
    except ImportError as e:
        print(f"✗ VoiceController import failed: {e}")
        return False
    
    try:
        from modules.gesture_control import GestureController
        print("✓ GestureController imported successfully")
    except ImportError as e:
        print(f"✗ GestureController import failed: {e}")
        return False
    
    try:
        from modules.system_tray import SystemTrayApp
        print("✓ SystemTrayApp imported successfully")
    except ImportError as e:
        print(f"✗ SystemTrayApp import failed: {e}")
        return False
    
    try:
        from modules.web_interface import WebInterface
        print("✓ WebInterface imported successfully")
    except ImportError as e:
        print(f"✗ WebInterface import failed: {e}")
        return False
    
    return True

def test_configuration():
    """Test configuration loading"""
    print("\nTesting configuration...")
    
    try:
        import yaml
        
        if os.path.exists('config.yaml'):
            with open('config.yaml', 'r') as file:
                config = yaml.safe_load(file)
            print("✓ Configuration file loaded successfully")
            
            # Check required sections
            required_sections = ['voice', 'gesture', 'commands', 'system', 'web']
            for section in required_sections:
                if section in config:
                    print(f"  ✓ {section} section found")
                else:
                    print(f"  ✗ {section} section missing")
            
            return True
        else:
            print("✗ config.yaml file not found")
            return False
            
    except Exception as e:
        print(f"✗ Configuration test failed: {e}")
        return False

def test_command_executor():
    """Test command executor functionality"""
    print("\nTesting CommandExecutor...")
    
    try:
        from modules.command_executor import CommandExecutor
        import yaml
        
        # Load config
        with open('config.yaml', 'r') as file:
            config = yaml.safe_load(file)
        
        # Create command executor
        executor = CommandExecutor(config)
        print("✓ CommandExecutor created successfully")
        
        # Test getting available commands
        commands = executor.get_available_commands()
        print(f"✓ Available commands retrieved: {len(commands.get('voice', []))} voice, {len(commands.get('gesture', []))} gesture")
        
        return True
        
    except Exception as e:
        print(f"✗ CommandExecutor test failed: {e}")
        return False

def test_microphone():
    """Test microphone availability"""
    print("\nTesting microphone...")
    
    try:
        import speech_recognition as sr
        
        recognizer = sr.Recognizer()
        microphone = sr.Microphone()
        
        print("✓ Microphone object created")
        
        # Test microphone access
        with microphone as source:
            recognizer.adjust_for_ambient_noise(source, duration=1)
        
        print("✓ Microphone access successful")
        return True
        
    except Exception as e:
        print(f"✗ Microphone test failed: {e}")
        return False

def test_camera():
    """Test camera availability"""
    print("\nTesting camera...")
    
    try:
        import cv2
        
        # Try to open camera
        cap = cv2.VideoCapture(0)
        
        if cap.isOpened():
            print("✓ Camera opened successfully")
            
            # Try to read a frame
            ret, frame = cap.read()
            if ret:
                print("✓ Camera frame captured successfully")
                result = True
            else:
                print("✗ Failed to capture camera frame")
                result = False
            
            cap.release()
            return result
        else:
            print("✗ Failed to open camera")
            return False
            
    except Exception as e:
        print(f"✗ Camera test failed: {e}")
        return False

def test_web_interface():
    """Test web interface creation"""
    print("\nTesting web interface...")
    
    try:
        from modules.web_interface import WebInterface
        import yaml
        
        # Load config
        with open('config.yaml', 'r') as file:
            config = yaml.safe_load(file)
        
        # Create a mock main app
        class MockApp:
            def __init__(self):
                self.running = False
                self.voice_enabled = True
                self.gesture_enabled = True
        
        mock_app = MockApp()
        
        # Create web interface
        web_interface = WebInterface(config, mock_app)
        print("✓ WebInterface created successfully")
        
        return True
        
    except Exception as e:
        print(f"✗ WebInterface test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("=" * 50)
    print("Voice and Gesture Control System - Test Suite")
    print("=" * 50)
    
    tests = [
        ("Import Tests", test_imports),
        ("Project Module Tests", test_project_modules),
        ("Configuration Tests", test_configuration),
        ("Command Executor Tests", test_command_executor),
        ("Microphone Tests", test_microphone),
        ("Camera Tests", test_camera),
        ("Web Interface Tests", test_web_interface),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{test_name}")
        print("-" * len(test_name))
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ Test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("TEST SUMMARY")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        symbol = "✓" if result else "✗"
        print(f"{symbol} {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! The system is ready to use.")
        print("\nTo start the system, run: python main.py")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Please check the requirements and setup.")
        print("\nRefer to README.md for installation instructions.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
