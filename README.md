# Voice and Gesture Control System

A Python-based voice and gesture control system that enables hands-free laptop control through voice commands and hand gestures.

## Features

### 🎤 Voice Control
- Real-time speech recognition with customizable wake word
- Configurable voice commands for system actions
- Noise cancellation and ambient noise adjustment
- Multi-language support

### 👋 Gesture Recognition
- MediaPipe-based hand gesture detection
- Support for basic gestures: thumbs up/down, open palm, fist, peace sign
- Real-time webcam processing
- Configurable detection sensitivity

### 🎛️ System Integration
- System tray application with start/stop controls
- Web-based dashboard for configuration and monitoring
- Background service operation
- Cross-platform compatibility (Windows, Linux, macOS)

### 🌐 Web Interface
- Real-time status monitoring
- Command configuration
- Component testing
- Settings management

## Installation

### Option 1: Automated Installation (Recommended)

1. **Navigate to the project directory**
   ```bash
   cd voice_and_ges
   ```

2. **Run the automated installer**
   ```bash
   python install_dependencies.py
   ```
   This script will handle platform-specific installations and dependencies.

### Option 2: Manual Installation

1. **Install minimal dependencies first**
   ```bash
   pip install -r requirements_minimal.txt
   ```

2. **Install platform-specific dependencies**

   **Windows:**
   ```bash
   # Install PyAudio (may require Visual C++ Build Tools)
   pip install pyaudio

   # Install PyQt5
   pip install PyQt5

   # For volume control: Download NirCmd and add to PATH (optional)
   ```

   **Linux (Ubuntu/Debian):**
   ```bash
   # Install system dependencies
   sudo apt-get update
   sudo apt-get install python3-pyaudio portaudio19-dev
   sudo apt-get install python3-pyqt5
   sudo apt-get install alsa-utils  # for volume control

   # Install Python packages
   pip install pyaudio PyQt5
   ```

   **macOS:**
   ```bash
   # Install system dependencies with Homebrew
   brew install portaudio

   # Install Python packages
   pip install pyaudio PyQt5
   ```

3. **Install remaining dependencies**
   ```bash
   pip install -r requirements.txt
   ```

### Option 3: Using Conda (Alternative)

```bash
# Create conda environment
conda create -n voice_gesture python=3.9
conda activate voice_gesture

# Install packages via conda (handles system dependencies better)
conda install pyaudio opencv numpy flask pyyaml
pip install mediapipe speechrecognition pyautogui flask-socketio PyQt5
```

## Quick Start

1. **Run the application**
   ```bash
   python main.py
   ```

2. **Access the web interface**
   - Open your browser and go to `http://127.0.0.1:5000`
   - Use the dashboard to control the system

3. **System tray controls**
   - Look for the system tray icon
   - Right-click for menu options
   - Start/stop the system from the tray

## Default Commands

### Voice Commands (say "computer" first)
- "open browser" → Opens default web browser
- "scroll down" → Scrolls down in current window
- "scroll up" → Scrolls up in current window
- "volume up" → Increases system volume
- "volume down" → Decreases system volume
- "close window" → Closes current window
- "minimize window" → Minimizes current window

### Gesture Commands
- **Thumbs Up** → Volume up
- **Thumbs Down** → Volume down
- **Open Palm** → Pause media
- **Fist** → Play media
- **Peace Sign** → Take screenshot

## Configuration

Edit `config.yaml` to customize:

- **Wake word**: Change the voice activation phrase
- **Camera settings**: Select camera index and detection confidence
- **Command mappings**: Add or modify voice/gesture commands
- **System settings**: Auto-start, tray behavior, etc.

## Testing Components

Use the web interface or system tray menu to test:
- **Microphone**: Verify speech recognition is working
- **Camera**: Check gesture detection functionality

## Troubleshooting

### Installation Issues

1. **MediaPipe version error**
   ```
   ERROR: Could not find a version that satisfies the requirement mediapipe==0.10.7
   ```
   **Solution:** Use the updated requirements.txt or install latest version:
   ```bash
   pip install mediapipe  # installs latest available version
   ```

2. **PyAudio installation fails**
   ```
   ERROR: Microsoft Visual C++ 14.0 is required
   ```
   **Solutions:**
   - **Windows:** Install Microsoft Visual C++ Build Tools
   - **Alternative:** Download pre-compiled wheel from https://www.lfd.uci.edu/~gohlke/pythonlibs/#pyaudio
   - **Conda:** `conda install pyaudio`

3. **PyQt5 installation issues**
   **Solutions:**
   - Try: `pip install PyQt5-Qt5`
   - **Linux:** `sudo apt-get install python3-pyqt5`
   - **macOS:** `brew install pyqt5`

### Runtime Issues

1. **Microphone not working**
   - Check microphone permissions
   - Verify PyAudio installation: `python -c "import pyaudio; print('PyAudio OK')"`
   - Test with different microphone devices

2. **Camera not detected**
   - Check camera permissions
   - Try different camera indices (0, 1, 2...)
   - Ensure no other applications are using the camera
   - Test: `python -c "import cv2; cap=cv2.VideoCapture(0); print('Camera OK' if cap.isOpened() else 'Camera Failed')"`

3. **Voice recognition errors**
   - Check internet connection (Google Speech API)
   - Adjust energy threshold in config
   - Speak clearly after the wake word

4. **Gesture detection issues**
   - Ensure good lighting conditions
   - Keep hands visible in camera frame
   - Adjust detection confidence settings
   - Test MediaPipe: `python -c "import mediapipe; print('MediaPipe OK')"`

5. **Import errors**
   Run the test script to identify missing dependencies:
   ```bash
   python test_system.py
   ```

### System Requirements

- **Python**: 3.7 or higher
- **RAM**: Minimum 4GB (8GB recommended)
- **Camera**: Any USB or built-in webcam
- **Microphone**: Any USB or built-in microphone
- **Internet**: Required for speech recognition

## Development

### Project Structure
```
voice_and_ges/
├── main.py                 # Main application entry point
├── config.yaml            # Configuration file
├── requirements.txt       # Python dependencies
├── modules/               # Core modules
│   ├── voice_control.py   # Voice recognition
│   ├── gesture_control.py # Gesture detection
│   ├── command_executor.py # Command execution
│   ├── system_tray.py     # System tray app
│   └── web_interface.py   # Web dashboard
├── templates/             # HTML templates
│   ├── dashboard.html     # Main dashboard
│   └── settings.html      # Settings page
└── logs/                  # Log files
```

### Adding New Commands

1. **Voice Commands**: Edit the `commands.voice` section in `config.yaml`
2. **Gesture Commands**: Edit the `commands.gesture` section in `config.yaml`
3. **Custom Actions**: Add new action methods in `command_executor.py`

### Extending Functionality

- **New Gestures**: Modify `_classify_gesture()` in `gesture_control.py`
- **Advanced Speech**: Integrate with other speech recognition services
- **Custom Actions**: Add system-specific commands in `command_executor.py`

## License

This project is open source. Feel free to modify and distribute.

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review the logs in the `logs/` directory
3. Test individual components using the web interface

## Future Enhancements

- Machine learning-based custom gesture training
- Voice command learning and adaptation
- Mobile app integration
- Cloud-based speech recognition options
- Advanced macro creation tools
