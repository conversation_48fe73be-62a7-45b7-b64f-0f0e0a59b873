"""
Gesture Recognition Module
Handles hand gesture detection and recognition using MediaPipe
"""

import logging
import cv2
import mediapipe as mp
import numpy as np
import threading
import time
from typing import Dict, Any, Optional, Tuple

class GestureController:
    """Handles gesture recognition and command processing"""
    
    def __init__(self, config: Dict[str, Any], command_executor):
        self.config = config
        self.command_executor = command_executor
        self.logger = logging.getLogger(__name__)
        
        # Gesture configuration
        self.gesture_config = config.get('gesture', {})
        self.camera_index = self.gesture_config.get('camera_index', 0)
        self.detection_confidence = self.gesture_config.get('detection_confidence', 0.7)
        self.tracking_confidence = self.gesture_config.get('tracking_confidence', 0.5)
        self.max_hands = self.gesture_config.get('max_hands', 2)
        self.gesture_hold_time = self.gesture_config.get('gesture_hold_time', 1.0)
        
        # Initialize MediaPipe
        self.mp_hands = mp.solutions.hands
        self.mp_drawing = mp.solutions.drawing_utils
        self.hands = self.mp_hands.Hands(
            static_image_mode=False,
            max_num_hands=self.max_hands,
            min_detection_confidence=self.detection_confidence,
            min_tracking_confidence=self.tracking_confidence
        )
        
        # Camera and control
        self.cap = None
        self.running = False
        
        # Gesture tracking
        self.current_gesture = None
        self.gesture_start_time = None
        self.last_executed_gesture = None
        self.last_execution_time = 0
        
        self.logger.info("Gesture Controller initialized")
    
    def start(self):
        """Start gesture recognition"""
        if self.running:
            return
        
        # Initialize camera
        if not self._initialize_camera():
            self.logger.error("Failed to initialize camera")
            return
        
        self.running = True
        self.logger.info("Starting gesture recognition...")
        
        # Start gesture recognition thread
        gesture_thread = threading.Thread(target=self._gesture_loop, daemon=True)
        gesture_thread.start()
        
        self.logger.info("Gesture recognition started")
    
    def stop(self):
        """Stop gesture recognition"""
        self.running = False
        
        if self.cap:
            self.cap.release()
            self.cap = None
        
        cv2.destroyAllWindows()
        self.logger.info("Gesture recognition stopped")
    
    def _initialize_camera(self):
        """Initialize camera for gesture recognition"""
        try:
            self.cap = cv2.VideoCapture(self.camera_index)
            
            if not self.cap.isOpened():
                self.logger.error(f"Cannot open camera {self.camera_index}")
                return False
            
            # Set camera properties
            self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
            self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
            self.cap.set(cv2.CAP_PROP_FPS, 30)
            
            self.logger.info(f"Camera {self.camera_index} initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize camera: {e}")
            return False
    
    def _gesture_loop(self):
        """Main gesture recognition loop"""
        while self.running:
            try:
                ret, frame = self.cap.read()
                if not ret:
                    self.logger.warning("Failed to read frame from camera")
                    continue
                
                # Flip frame horizontally for mirror effect
                frame = cv2.flip(frame, 1)
                
                # Process frame for gesture detection
                gesture = self._process_frame(frame)
                
                # Handle gesture detection
                if gesture:
                    self._handle_gesture(gesture)
                
                # Optional: Display frame (for debugging)
                if self.config.get('debug', {}).get('show_camera', False):
                    cv2.imshow('Gesture Recognition', frame)
                    if cv2.waitKey(1) & 0xFF == ord('q'):
                        break
                
                time.sleep(0.03)  # ~30 FPS
                
            except Exception as e:
                self.logger.error(f"Error in gesture loop: {e}")
                time.sleep(1)
    
    def _process_frame(self, frame) -> Optional[str]:
        """Process frame and detect gestures"""
        try:
            # Convert BGR to RGB
            rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            
            # Process frame with MediaPipe
            results = self.hands.process(rgb_frame)
            
            if results.multi_hand_landmarks:
                for hand_landmarks in results.multi_hand_landmarks:
                    # Draw landmarks (optional, for debugging)
                    if self.config.get('debug', {}).get('show_landmarks', False):
                        self.mp_drawing.draw_landmarks(
                            frame, hand_landmarks, self.mp_hands.HAND_CONNECTIONS)
                    
                    # Classify gesture
                    gesture = self._classify_gesture(hand_landmarks)
                    if gesture:
                        return gesture
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error processing frame: {e}")
            return None
    
    def _classify_gesture(self, landmarks) -> Optional[str]:
        """Classify hand gesture based on landmarks"""
        try:
            # Extract landmark positions
            landmark_points = []
            for lm in landmarks.landmark:
                landmark_points.append([lm.x, lm.y])
            
            landmark_points = np.array(landmark_points)
            
            # Simple gesture classification based on finger positions
            # This is a basic implementation - can be enhanced with ML models
            
            # Get finger tip and pip positions
            thumb_tip = landmark_points[4]
            thumb_ip = landmark_points[3]
            index_tip = landmark_points[8]
            index_pip = landmark_points[6]
            middle_tip = landmark_points[12]
            middle_pip = landmark_points[10]
            ring_tip = landmark_points[16]
            ring_pip = landmark_points[14]
            pinky_tip = landmark_points[20]
            pinky_pip = landmark_points[18]
            
            # Check if fingers are extended
            thumb_up = thumb_tip[1] < thumb_ip[1]
            index_up = index_tip[1] < index_pip[1]
            middle_up = middle_tip[1] < middle_pip[1]
            ring_up = ring_tip[1] < ring_pip[1]
            pinky_up = pinky_tip[1] < pinky_pip[1]
            
            # Gesture classification
            fingers_up = [thumb_up, index_up, middle_up, ring_up, pinky_up]
            total_fingers = sum(fingers_up)
            
            # Thumbs up: only thumb extended
            if thumb_up and not any([index_up, middle_up, ring_up, pinky_up]):
                return "thumbs_up"
            
            # Thumbs down: thumb down, others may be up
            elif not thumb_up and thumb_tip[1] > thumb_ip[1] + 0.05:
                return "thumbs_down"
            
            # Open palm: all fingers extended
            elif total_fingers >= 4:
                return "open_palm"
            
            # Fist: no fingers extended
            elif total_fingers == 0:
                return "fist"
            
            # Peace sign: index and middle fingers up
            elif index_up and middle_up and not ring_up and not pinky_up:
                return "peace"
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error classifying gesture: {e}")
            return None
    
    def _handle_gesture(self, gesture: str):
        """Handle detected gesture"""
        current_time = time.time()
        
        # Check if this is a new gesture
        if gesture != self.current_gesture:
            self.current_gesture = gesture
            self.gesture_start_time = current_time
            return
        
        # Check if gesture has been held long enough
        if current_time - self.gesture_start_time < self.gesture_hold_time:
            return
        
        # Prevent rapid repeated execution
        if (gesture == self.last_executed_gesture and 
            current_time - self.last_execution_time < 2.0):
            return
        
        # Execute gesture command
        self.logger.info(f"Gesture detected: {gesture}")
        success = self.command_executor.execute_gesture_command(gesture)
        
        if success:
            self.last_executed_gesture = gesture
            self.last_execution_time = current_time
            self.logger.info(f"Gesture '{gesture}' executed successfully")
        else:
            self.logger.warning(f"Failed to execute gesture: '{gesture}'")
        
        # Reset gesture tracking
        self.current_gesture = None
        self.gesture_start_time = None
    
    def get_status(self):
        """Get current status of gesture controller"""
        return {
            'running': self.running,
            'camera_available': self.cap is not None and self.cap.isOpened(),
            'current_gesture': self.current_gesture,
            'last_executed_gesture': self.last_executed_gesture,
            'camera_index': self.camera_index
        }
    
    def test_camera(self):
        """Test camera functionality"""
        try:
            test_cap = cv2.VideoCapture(self.camera_index)
            if not test_cap.isOpened():
                return False, f"Cannot open camera {self.camera_index}"
            
            ret, frame = test_cap.read()
            test_cap.release()
            
            if ret:
                return True, f"Camera {self.camera_index} is working"
            else:
                return False, f"Camera {self.camera_index} cannot capture frames"
                
        except Exception as e:
            return False, f"Camera test failed: {e}"
