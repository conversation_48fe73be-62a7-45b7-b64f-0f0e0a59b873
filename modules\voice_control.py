"""
Voice Control Module
Handles speech recognition and voice command processing
"""

import logging
import speech_recognition as sr
import pyaudio
import threading
import time
from typing import Dict, Any, Callable

class VoiceController:
    """Handles voice recognition and command processing"""
    
    def __init__(self, config: Dict[str, Any], command_executor, activity_manager=None):
        self.config = config
        self.command_executor = command_executor
        self.activity_manager = activity_manager
        self.logger = logging.getLogger(__name__)
        
        # Voice configuration
        self.voice_config = config.get('voice', {})
        self.wake_word = self.voice_config.get('wake_word', 'computer').lower()
        self.language = self.voice_config.get('language', 'en-US')
        self.timeout = self.voice_config.get('timeout', 5)
        self.phrase_timeout = self.voice_config.get('phrase_timeout', 0.3)
        
        # Initialize speech recognizer
        self.recognizer = sr.Recognizer()
        self.microphone = None
        
        # Control flags
        self.running = False
        self.listening = False
        self.wake_word_detected = False
        
        # Initialize microphone
        self._initialize_microphone()
        
        self.logger.info("Voice Controller initialized")
    
    def _initialize_microphone(self):
        """Initialize microphone with optimal settings"""
        try:
            self.microphone = sr.Microphone()
            
            # Adjust for ambient noise
            with self.microphone as source:
                self.logger.info("Adjusting for ambient noise... Please wait.")
                self.recognizer.adjust_for_ambient_noise(source, duration=2)
                
            # Set energy threshold
            if self.voice_config.get('dynamic_energy_threshold', True):
                self.recognizer.dynamic_energy_threshold = True
            else:
                self.recognizer.energy_threshold = self.voice_config.get('energy_threshold', 300)
            
            self.logger.info("Microphone initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize microphone: {e}")
            self.microphone = None
    
    def start(self):
        """Start voice recognition"""
        if self.running or not self.microphone:
            return
        
        self.running = True
        self.logger.info("Starting voice recognition...")
        
        # Start listening thread
        listen_thread = threading.Thread(target=self._listen_loop, daemon=True)
        listen_thread.start()
        
        self.logger.info("Voice recognition started")
    
    def stop(self):
        """Stop voice recognition"""
        self.running = False
        self.listening = False
        self.logger.info("Voice recognition stopped")
    
    def _listen_loop(self):
        """Main listening loop"""
        while self.running:
            try:
                if not self.listening:
                    # Listen for wake word
                    self._listen_for_wake_word()
                else:
                    # Listen for commands
                    self._listen_for_command()
                    
            except Exception as e:
                self.logger.error(f"Error in listening loop: {e}")
                time.sleep(1)
    
    def _listen_for_wake_word(self):
        """Listen for the wake word"""
        try:
            with self.microphone as source:
                # Listen for audio
                audio = self.recognizer.listen(source, timeout=1, phrase_time_limit=3)
            
            # Recognize speech
            text = self.recognizer.recognize_google(audio, language=self.language).lower()
            
            if self.wake_word in text:
                self.logger.info(f"Wake word '{self.wake_word}' detected!")
                self.wake_word_detected = True
                self.listening = True

                # Track wake word detection activity
                if self.activity_manager:
                    details = {
                        'wake_word': self.wake_word,
                        'detected_text': text,
                        'language': self.language
                    }
                    self.activity_manager.add_activity(
                        activity_type='voice',
                        action=f"wake_word_detected",
                        details=details,
                        success=True
                    )

                # Give audio feedback (optional)
                self._play_wake_sound()

                # Start command timeout
                threading.Timer(self.timeout, self._reset_listening).start()
                
        except sr.WaitTimeoutError:
            pass  # Normal timeout, continue listening
        except sr.UnknownValueError:
            pass  # Could not understand audio
        except sr.RequestError as e:
            self.logger.error(f"Speech recognition service error: {e}")
            time.sleep(1)
    
    def _listen_for_command(self):
        """Listen for voice commands after wake word"""
        try:
            with self.microphone as source:
                self.logger.info("Listening for command...")
                audio = self.recognizer.listen(source, timeout=self.timeout,
                                             phrase_time_limit=5)

            # Recognize command
            command = self.recognizer.recognize_google(audio, language=self.language)
            self.logger.info(f"Command recognized: '{command}'")

            # Track activity start time for duration calculation
            activity_start_time = time.time()

            # Execute command
            success = self.command_executor.execute_voice_command(command)

            # Calculate execution duration
            duration = time.time() - activity_start_time

            # Track activity in Activity Manager
            if self.activity_manager:
                # Find the action that was executed
                action = 'unknown_action'
                for voice_cmd, voice_action in self.command_executor.voice_commands.items():
                    if voice_cmd.lower() in command.lower():
                        action = voice_action
                        break

                details = {
                    'command': command,
                    'action': action,
                    'wake_word': self.wake_word,
                    'language': self.language,
                    'confidence': 'high'  # Google Speech API doesn't provide confidence scores
                }
                self.activity_manager.add_activity(
                    activity_type='voice',
                    action=command,
                    details=details,
                    success=success,
                    duration=duration
                )

            if success:
                self.logger.info(f"Command '{command}' executed successfully")
            else:
                self.logger.warning(f"Failed to execute command: '{command}'")

            # Reset listening state
            self._reset_listening()
            
        except sr.WaitTimeoutError:
            self.logger.info("Command timeout - no command received")
            self._reset_listening()
        except sr.UnknownValueError:
            self.logger.warning("Could not understand the command")
            self._reset_listening()
        except sr.RequestError as e:
            self.logger.error(f"Speech recognition service error: {e}")
            self._reset_listening()
    
    def _reset_listening(self):
        """Reset listening state"""
        self.listening = False
        self.wake_word_detected = False
        self.logger.info("Listening state reset - waiting for wake word")
    
    def _play_wake_sound(self):
        """Play a sound to indicate wake word detection (optional)"""
        # This could play a beep or sound file
        # For now, just log
        self.logger.info("Wake word acknowledged")
    
    def set_wake_word(self, wake_word: str):
        """Change the wake word"""
        self.wake_word = wake_word.lower()
        self.logger.info(f"Wake word changed to: '{self.wake_word}'")
    
    def get_status(self):
        """Get current status of voice controller"""
        return {
            'running': self.running,
            'listening': self.listening,
            'wake_word': self.wake_word,
            'wake_word_detected': self.wake_word_detected,
            'microphone_available': self.microphone is not None
        }
    
    def test_microphone(self):
        """Test microphone functionality"""
        if not self.microphone:
            return False, "Microphone not initialized"
        
        try:
            with self.microphone as source:
                self.logger.info("Testing microphone... Say something!")
                audio = self.recognizer.listen(source, timeout=5, phrase_time_limit=3)
            
            text = self.recognizer.recognize_google(audio, language=self.language)
            return True, f"Microphone test successful. Heard: '{text}'"
            
        except sr.WaitTimeoutError:
            return False, "Microphone test timeout - no audio detected"
        except sr.UnknownValueError:
            return False, "Microphone working but could not understand audio"
        except sr.RequestError as e:
            return False, f"Speech recognition service error: {e}"
        except Exception as e:
            return False, f"Microphone test failed: {e}"
