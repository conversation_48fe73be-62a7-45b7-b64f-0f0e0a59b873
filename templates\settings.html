<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Settings - Voice & Gesture Control</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            margin: 0;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .content {
            padding: 30px;
        }
        
        .section {
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #4facfe;
        }
        
        .section h3 {
            margin-top: 0;
            color: #333;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        
        .form-group input, .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn:hover {
            opacity: 0.9;
            transform: translateY(-1px);
        }
        
        .back-link {
            display: inline-block;
            margin-bottom: 20px;
            color: #007bff;
            text-decoration: none;
        }
        
        .back-link:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⚙️ Settings</h1>
            <p>Configure your voice and gesture control system</p>
        </div>
        
        <div class="content">
            <a href="/" class="back-link">← Back to Dashboard</a>
            
            <div class="section">
                <h3>🎤 Voice Control Settings</h3>
                <div class="form-group">
                    <label for="wake-word">Wake Word:</label>
                    <input type="text" id="wake-word" value="{{ config.voice.wake_word if config.voice else 'computer' }}">
                </div>
                <div class="form-group">
                    <label for="language">Language:</label>
                    <select id="language">
                        <option value="en-US">English (US)</option>
                        <option value="en-GB">English (UK)</option>
                        <option value="es-ES">Spanish</option>
                        <option value="fr-FR">French</option>
                        <option value="de-DE">German</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="timeout">Command Timeout (seconds):</label>
                    <input type="number" id="timeout" min="1" max="10" value="{{ config.voice.timeout if config.voice else 5 }}">
                </div>
            </div>
            
            <div class="section">
                <h3>👋 Gesture Control Settings</h3>
                <div class="form-group">
                    <label for="camera-index">Camera Index:</label>
                    <input type="number" id="camera-index" min="0" max="5" value="{{ config.gesture.camera_index if config.gesture else 0 }}">
                </div>
                <div class="form-group">
                    <label for="detection-confidence">Detection Confidence:</label>
                    <input type="range" id="detection-confidence" min="0.1" max="1.0" step="0.1" value="{{ config.gesture.detection_confidence if config.gesture else 0.7 }}">
                    <span id="detection-value">0.7</span>
                </div>
                <div class="form-group">
                    <label for="gesture-hold-time">Gesture Hold Time (seconds):</label>
                    <input type="number" id="gesture-hold-time" min="0.5" max="5.0" step="0.1" value="{{ config.gesture.gesture_hold_time if config.gesture else 1.0 }}">
                </div>
            </div>
            
            <div class="section">
                <h3>🌐 Web Interface Settings</h3>
                <div class="form-group">
                    <label for="web-host">Host:</label>
                    <input type="text" id="web-host" value="{{ config.web.host if config.web else '127.0.0.1' }}">
                </div>
                <div class="form-group">
                    <label for="web-port">Port:</label>
                    <input type="number" id="web-port" min="1000" max="65535" value="{{ config.web.port if config.web else 5000 }}">
                </div>
            </div>
            
            <div class="section">
                <h3>🔧 System Settings</h3>
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="auto-start" {{ 'checked' if config.system and config.system.auto_start else '' }}>
                        Auto-start on system boot
                    </label>
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="minimize-to-tray" {{ 'checked' if config.system and config.system.minimize_to_tray else '' }}>
                        Minimize to system tray
                    </label>
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="energy-saving" {{ 'checked' if config.system and config.system.energy_saving else '' }}>
                        Energy saving mode
                    </label>
                </div>
            </div>
            
            <div style="text-align: center; margin-top: 30px;">
                <button class="btn btn-primary" onclick="saveSettings()">Save Settings</button>
                <button class="btn btn-secondary" onclick="resetSettings()">Reset to Defaults</button>
            </div>
        </div>
    </div>
    
    <script>
        // Update detection confidence display
        document.getElementById('detection-confidence').addEventListener('input', function() {
            document.getElementById('detection-value').textContent = this.value;
        });
        
        // Save settings
        function saveSettings() {
            const settings = {
                voice: {
                    wake_word: document.getElementById('wake-word').value,
                    language: document.getElementById('language').value,
                    timeout: parseInt(document.getElementById('timeout').value)
                },
                gesture: {
                    camera_index: parseInt(document.getElementById('camera-index').value),
                    detection_confidence: parseFloat(document.getElementById('detection-confidence').value),
                    gesture_hold_time: parseFloat(document.getElementById('gesture-hold-time').value)
                },
                web: {
                    host: document.getElementById('web-host').value,
                    port: parseInt(document.getElementById('web-port').value)
                },
                system: {
                    auto_start: document.getElementById('auto-start').checked,
                    minimize_to_tray: document.getElementById('minimize-to-tray').checked,
                    energy_saving: document.getElementById('energy-saving').checked
                }
            };
            
            fetch('/api/config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(settings)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Settings saved successfully! Please restart the system for changes to take effect.');
                } else {
                    alert('Error saving settings: ' + (data.error || 'Unknown error'));
                }
            })
            .catch(error => {
                alert('Network error: ' + error.message);
            });
        }
        
        // Reset settings
        function resetSettings() {
            if (confirm('Are you sure you want to reset all settings to defaults?')) {
                // Reset form to default values
                document.getElementById('wake-word').value = 'computer';
                document.getElementById('language').value = 'en-US';
                document.getElementById('timeout').value = 5;
                document.getElementById('camera-index').value = 0;
                document.getElementById('detection-confidence').value = 0.7;
                document.getElementById('detection-value').textContent = '0.7';
                document.getElementById('gesture-hold-time').value = 1.0;
                document.getElementById('web-host').value = '127.0.0.1';
                document.getElementById('web-port').value = 5000;
                document.getElementById('auto-start').checked = false;
                document.getElementById('minimize-to-tray').checked = true;
                document.getElementById('energy-saving').checked = true;
            }
        }
    </script>
</body>
</html>
