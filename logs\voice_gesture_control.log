2025-06-18 21:24:11,530 - modules.command_executor - INFO - Command Executor initialized
2025-06-18 21:24:11,765 - modules.voice_control - INFO - Adjusting for ambient noise... Please wait.
2025-06-18 21:24:13,832 - modules.voice_control - INFO - Microphone initialized successfully
2025-06-18 21:24:13,832 - modules.voice_control - INFO - Voice Controller initialized
2025-06-18 21:24:13,845 - modules.gesture_control - INFO - Gesture Controller initialized
2025-06-18 21:24:13,920 - modules.web_interface - INFO - Web Interface initialized
2025-06-18 21:24:14,013 - modules.system_tray - INFO - System tray icon created successfully
2025-06-18 21:24:14,015 - modules.system_tray - INFO - System Tray Application initialized
2025-06-18 21:24:14,016 - __main__ - INFO - Starting Voice and Gesture Control System
2025-06-18 21:24:14,018 - modules.web_interface - INFO - Status update thread started
2025-06-18 21:24:14,018 - modules.web_interface - INFO - Starting web interface on 127.0.0.1:5000
2025-06-18 21:24:14,019 - modules.voice_control - INFO - Starting voice recognition...
2025-06-18 21:24:14,020 - __main__ - INFO - All modules started successfully
2025-06-18 21:24:14,022 - modules.system_tray - INFO - Starting system tray application
2025-06-18 21:24:14,022 - modules.voice_control - INFO - Voice recognition started
2025-06-18 21:24:21,389 - modules.gesture_control - INFO - Camera 0 initialized successfully
2025-06-18 21:24:21,389 - modules.gesture_control - INFO - Starting gesture recognition...
2025-06-18 21:24:21,390 - modules.gesture_control - INFO - Gesture recognition started
2025-06-18 21:24:24,179 - modules.gesture_control - INFO - Gesture detected: fist
2025-06-18 21:24:24,180 - modules.command_executor - INFO - Executing gesture command 'fist' -> action 'play_media'
2025-06-18 21:24:24,284 - modules.command_executor - INFO - Played media
2025-06-18 21:24:24,284 - modules.gesture_control - INFO - Gesture 'fist' executed successfully
2025-06-18 21:24:26,281 - modules.gesture_control - INFO - Gesture detected: fist
2025-06-18 21:24:26,283 - modules.command_executor - INFO - Executing gesture command 'fist' -> action 'play_media'
2025-06-18 21:24:26,391 - modules.command_executor - INFO - Played media
2025-06-18 21:24:26,391 - modules.gesture_control - INFO - Gesture 'fist' executed successfully
2025-06-18 21:24:28,295 - modules.gesture_control - INFO - Gesture detected: fist
2025-06-18 21:24:28,296 - modules.command_executor - INFO - Executing gesture command 'fist' -> action 'play_media'
2025-06-18 21:24:28,399 - modules.command_executor - INFO - Played media
2025-06-18 21:24:28,399 - modules.gesture_control - INFO - Gesture 'fist' executed successfully
2025-06-18 21:24:30,321 - modules.gesture_control - INFO - Gesture detected: fist
2025-06-18 21:24:30,322 - modules.command_executor - INFO - Executing gesture command 'fist' -> action 'play_media'
2025-06-18 21:24:30,429 - modules.command_executor - INFO - Played media
2025-06-18 21:24:30,430 - modules.gesture_control - INFO - Gesture 'fist' executed successfully
2025-06-18 21:24:32,365 - modules.gesture_control - INFO - Gesture detected: fist
2025-06-18 21:24:32,365 - modules.command_executor - INFO - Executing gesture command 'fist' -> action 'play_media'
2025-06-18 21:24:32,470 - modules.command_executor - INFO - Played media
2025-06-18 21:24:32,470 - modules.gesture_control - INFO - Gesture 'fist' executed successfully
2025-06-18 21:24:34,390 - modules.gesture_control - INFO - Gesture detected: fist
2025-06-18 21:24:34,391 - modules.command_executor - INFO - Executing gesture command 'fist' -> action 'play_media'
2025-06-18 21:24:34,496 - modules.command_executor - INFO - Played media
2025-06-18 21:24:34,496 - modules.gesture_control - INFO - Gesture 'fist' executed successfully
2025-06-18 21:24:40,175 - modules.gesture_control - INFO - Gesture detected: fist
2025-06-18 21:24:40,175 - modules.command_executor - INFO - Executing gesture command 'fist' -> action 'play_media'
2025-06-18 21:24:40,280 - modules.command_executor - INFO - Played media
2025-06-18 21:24:40,280 - modules.gesture_control - INFO - Gesture 'fist' executed successfully
2025-06-18 21:24:42,197 - modules.gesture_control - INFO - Gesture detected: fist
2025-06-18 21:24:42,198 - modules.command_executor - INFO - Executing gesture command 'fist' -> action 'play_media'
2025-06-18 21:24:42,305 - modules.command_executor - INFO - Played media
2025-06-18 21:24:42,305 - modules.gesture_control - INFO - Gesture 'fist' executed successfully
2025-06-18 21:24:44,224 - modules.gesture_control - INFO - Gesture detected: thumbs_up
2025-06-18 21:24:44,225 - modules.command_executor - INFO - Executing gesture command 'thumbs_up' -> action 'volume_up'
2025-06-18 21:24:44,230 - modules.command_executor - ERROR - Error executing action 'volume_up': [WinError 2] The system cannot find the file specified
2025-06-18 21:24:44,230 - modules.gesture_control - WARNING - Failed to execute gesture: 'thumbs_up'
2025-06-18 21:24:45,336 - modules.gesture_control - INFO - Gesture detected: thumbs_up
2025-06-18 21:24:45,337 - modules.command_executor - INFO - Executing gesture command 'thumbs_up' -> action 'volume_up'
2025-06-18 21:24:45,341 - modules.command_executor - ERROR - Error executing action 'volume_up': [WinError 2] The system cannot find the file specified
2025-06-18 21:24:45,342 - modules.gesture_control - WARNING - Failed to execute gesture: 'thumbs_up'
2025-06-18 21:24:46,422 - modules.gesture_control - INFO - Gesture detected: thumbs_up
2025-06-18 21:24:46,422 - modules.command_executor - INFO - Executing gesture command 'thumbs_up' -> action 'volume_up'
2025-06-18 21:24:46,427 - modules.command_executor - ERROR - Error executing action 'volume_up': [WinError 2] The system cannot find the file specified
2025-06-18 21:24:46,428 - modules.gesture_control - WARNING - Failed to execute gesture: 'thumbs_up'
2025-06-18 21:24:47,578 - modules.gesture_control - INFO - Gesture detected: thumbs_up
2025-06-18 21:24:47,578 - modules.command_executor - INFO - Executing gesture command 'thumbs_up' -> action 'volume_up'
2025-06-18 21:24:47,583 - modules.command_executor - ERROR - Error executing action 'volume_up': [WinError 2] The system cannot find the file specified
2025-06-18 21:24:47,583 - modules.gesture_control - WARNING - Failed to execute gesture: 'thumbs_up'
2025-06-18 21:24:48,696 - modules.gesture_control - INFO - Gesture detected: thumbs_up
2025-06-18 21:24:48,698 - modules.command_executor - INFO - Executing gesture command 'thumbs_up' -> action 'volume_up'
2025-06-18 21:24:48,703 - modules.command_executor - ERROR - Error executing action 'volume_up': [WinError 2] The system cannot find the file specified
2025-06-18 21:24:48,704 - modules.gesture_control - WARNING - Failed to execute gesture: 'thumbs_up'
2025-06-18 21:24:49,798 - modules.gesture_control - INFO - Gesture detected: thumbs_up
2025-06-18 21:24:49,799 - modules.command_executor - INFO - Executing gesture command 'thumbs_up' -> action 'volume_up'
2025-06-18 21:24:49,803 - modules.command_executor - ERROR - Error executing action 'volume_up': [WinError 2] The system cannot find the file specified
2025-06-18 21:24:49,803 - modules.gesture_control - WARNING - Failed to execute gesture: 'thumbs_up'
2025-06-18 21:24:54,915 - modules.gesture_control - INFO - Gesture detected: thumbs_up
2025-06-18 21:24:54,916 - modules.command_executor - INFO - Executing gesture command 'thumbs_up' -> action 'volume_up'
2025-06-18 21:24:54,920 - modules.command_executor - ERROR - Error executing action 'volume_up': [WinError 2] The system cannot find the file specified
2025-06-18 21:24:54,921 - modules.gesture_control - WARNING - Failed to execute gesture: 'thumbs_up'
2025-06-18 21:24:56,053 - modules.gesture_control - INFO - Gesture detected: thumbs_up
2025-06-18 21:24:56,054 - modules.command_executor - INFO - Executing gesture command 'thumbs_up' -> action 'volume_up'
2025-06-18 21:24:56,058 - modules.command_executor - ERROR - Error executing action 'volume_up': [WinError 2] The system cannot find the file specified
2025-06-18 21:24:56,058 - modules.gesture_control - WARNING - Failed to execute gesture: 'thumbs_up'
2025-06-18 21:24:58,112 - modules.gesture_control - INFO - Gesture detected: thumbs_up
2025-06-18 21:24:58,113 - modules.command_executor - INFO - Executing gesture command 'thumbs_up' -> action 'volume_up'
2025-06-18 21:24:58,116 - modules.command_executor - ERROR - Error executing action 'volume_up': [WinError 2] The system cannot find the file specified
2025-06-18 21:24:58,116 - modules.gesture_control - WARNING - Failed to execute gesture: 'thumbs_up'
2025-06-19 10:37:01,468 - modules.command_executor - INFO - Command Executor initialized
2025-06-19 10:37:01,657 - modules.voice_control - INFO - Adjusting for ambient noise... Please wait.
2025-06-19 10:37:03,706 - modules.voice_control - INFO - Microphone initialized successfully
2025-06-19 10:37:03,707 - modules.voice_control - INFO - Voice Controller initialized
2025-06-19 10:37:03,729 - modules.gesture_control - INFO - Gesture Controller initialized
2025-06-19 10:37:03,807 - modules.web_interface - INFO - Web Interface initialized
2025-06-19 10:37:03,861 - modules.system_tray - INFO - System tray icon created successfully
2025-06-19 10:37:03,862 - modules.system_tray - INFO - System Tray Application initialized
2025-06-19 10:37:03,863 - __main__ - INFO - Starting Voice and Gesture Control System
2025-06-19 10:37:03,863 - modules.web_interface - INFO - Status update thread started
2025-06-19 10:37:03,863 - modules.voice_control - INFO - Starting voice recognition...
2025-06-19 10:37:03,864 - modules.web_interface - INFO - Starting web interface on 127.0.0.1:5000
2025-06-19 10:37:03,864 - __main__ - INFO - All modules started successfully
2025-06-19 10:37:03,866 - modules.voice_control - INFO - Voice recognition started
2025-06-19 10:37:03,867 - modules.system_tray - INFO - Starting system tray application
2025-06-19 10:37:07,355 - modules.gesture_control - INFO - Camera 0 initialized successfully
2025-06-19 10:37:07,356 - modules.gesture_control - INFO - Starting gesture recognition...
2025-06-19 10:37:07,357 - modules.gesture_control - INFO - Gesture recognition started
2025-06-19 10:37:08,780 - geventwebsocket.handler - INFO - 127.0.0.1 - - [2025-06-19 10:37:08] "GET / HTTP/1.1" 200 13706 0.007010
2025-06-19 10:37:09,024 - geventwebsocket.handler - INFO - 127.0.0.1 - - [2025-06-19 10:37:09] "GET /socket.io/?EIO=4&transport=polling&t=PU6bMnU HTTP/1.1" 200 276 0.001010
2025-06-19 10:37:09,084 - geventwebsocket.handler - INFO - 127.0.0.1 - - [2025-06-19 10:37:09] "GET /api/commands HTTP/1.1" 200 286 0.001009
2025-06-19 10:37:09,089 - modules.web_interface - INFO - Client connected to SocketIO
2025-06-19 10:37:09,090 - geventwebsocket.handler - INFO - 127.0.0.1 - - [2025-06-19 10:37:09] "POST /socket.io/?EIO=4&transport=polling&t=PU6bMoR&sid=qmPYXJgMEM7VVeCAAAAA HTTP/1.1" 200 195 0.001000
2025-06-19 10:37:09,092 - geventwebsocket.handler - INFO - 127.0.0.1 - - [2025-06-19 10:37:09] "GET /socket.io/?EIO=4&transport=polling&t=PU6bMoT&sid=qmPYXJgMEM7VVeCAAAAA HTTP/1.1" 200 546 0.000000
2025-06-19 10:37:09,095 - geventwebsocket.handler - INFO - 127.0.0.1 - - [2025-06-19 10:37:09] "GET /favicon.ico HTTP/1.1" 404 331 0.001000
2025-06-19 10:37:09,096 - geventwebsocket.handler - INFO - 127.0.0.1 - - [2025-06-19 10:37:09] "GET /api/commands HTTP/1.1" 200 286 0.001001
2025-06-19 10:37:09,097 - geventwebsocket.handler - INFO - 127.0.0.1 - - [2025-06-19 10:37:09] "GET /socket.io/?EIO=4&transport=polling&t=PU6bMoc&sid=qmPYXJgMEM7VVeCAAAAA HTTP/1.1" 200 157 0.000000
2025-06-19 10:37:25,051 - modules.gesture_control - INFO - Gesture detected: thumbs_up
2025-06-19 10:37:25,052 - modules.command_executor - INFO - Executing gesture command 'thumbs_up' -> action 'volume_up'
2025-06-19 10:37:25,166 - modules.command_executor - INFO - Volume increased
2025-06-19 10:37:25,166 - modules.gesture_control - INFO - Gesture 'thumbs_up' executed successfully
2025-06-19 10:37:33,988 - modules.gesture_control - INFO - Gesture detected: thumbs_down
2025-06-19 10:37:33,988 - modules.command_executor - INFO - Executing gesture command 'thumbs_down' -> action 'volume_down'
2025-06-19 10:37:34,099 - modules.command_executor - INFO - Volume decreased
2025-06-19 10:37:34,099 - modules.gesture_control - INFO - Gesture 'thumbs_down' executed successfully
2025-06-19 10:37:36,034 - modules.gesture_control - INFO - Gesture detected: thumbs_down
2025-06-19 10:37:36,034 - modules.command_executor - INFO - Executing gesture command 'thumbs_down' -> action 'volume_down'
2025-06-19 10:37:36,142 - modules.command_executor - INFO - Volume decreased
2025-06-19 10:37:36,142 - modules.gesture_control - INFO - Gesture 'thumbs_down' executed successfully
2025-06-19 10:37:38,048 - modules.gesture_control - INFO - Gesture detected: thumbs_down
2025-06-19 10:37:38,048 - modules.command_executor - INFO - Executing gesture command 'thumbs_down' -> action 'volume_down'
2025-06-19 10:37:38,156 - modules.command_executor - INFO - Volume decreased
2025-06-19 10:37:38,156 - modules.gesture_control - INFO - Gesture 'thumbs_down' executed successfully
2025-06-19 10:37:40,108 - modules.gesture_control - INFO - Gesture detected: thumbs_down
2025-06-19 10:37:40,109 - modules.command_executor - INFO - Executing gesture command 'thumbs_down' -> action 'volume_down'
2025-06-19 10:37:40,217 - modules.command_executor - INFO - Volume decreased
2025-06-19 10:37:40,217 - modules.gesture_control - INFO - Gesture 'thumbs_down' executed successfully
2025-06-19 10:37:45,035 - modules.gesture_control - INFO - Gesture detected: fist
2025-06-19 10:37:45,036 - modules.command_executor - INFO - Executing gesture command 'fist' -> action 'play_media'
2025-06-19 10:37:45,139 - modules.command_executor - INFO - Played media
2025-06-19 10:37:45,139 - modules.gesture_control - INFO - Gesture 'fist' executed successfully
2025-06-19 10:37:48,552 - modules.gesture_control - INFO - Gesture detected: open_palm
2025-06-19 10:37:48,552 - modules.command_executor - INFO - Executing gesture command 'open_palm' -> action 'pause_media'
2025-06-19 10:37:48,655 - modules.command_executor - INFO - Paused media
2025-06-19 10:37:48,655 - modules.gesture_control - INFO - Gesture 'open_palm' executed successfully
2025-06-19 10:37:50,600 - modules.gesture_control - INFO - Gesture detected: open_palm
2025-06-19 10:37:50,600 - modules.command_executor - INFO - Executing gesture command 'open_palm' -> action 'pause_media'
2025-06-19 10:37:50,703 - modules.command_executor - INFO - Paused media
2025-06-19 10:37:50,703 - modules.gesture_control - INFO - Gesture 'open_palm' executed successfully
2025-06-19 10:37:52,606 - modules.gesture_control - INFO - Gesture detected: open_palm
2025-06-19 10:37:52,607 - modules.command_executor - INFO - Executing gesture command 'open_palm' -> action 'pause_media'
2025-06-19 10:37:52,710 - modules.command_executor - INFO - Paused media
2025-06-19 10:37:52,710 - modules.gesture_control - INFO - Gesture 'open_palm' executed successfully
2025-06-19 10:37:54,730 - modules.gesture_control - INFO - Gesture detected: open_palm
2025-06-19 10:37:54,730 - modules.command_executor - INFO - Executing gesture command 'open_palm' -> action 'pause_media'
2025-06-19 10:37:54,833 - modules.command_executor - INFO - Paused media
2025-06-19 10:37:54,833 - modules.gesture_control - INFO - Gesture 'open_palm' executed successfully
2025-06-19 10:37:56,470 - modules.gesture_control - INFO - Gesture detected: thumbs_up
2025-06-19 10:37:56,471 - modules.command_executor - INFO - Executing gesture command 'thumbs_up' -> action 'volume_up'
2025-06-19 10:37:56,582 - modules.command_executor - INFO - Volume increased
2025-06-19 10:37:56,582 - modules.gesture_control - INFO - Gesture 'thumbs_up' executed successfully
2025-06-19 10:37:57,640 - modules.gesture_control - INFO - Gesture detected: open_palm
2025-06-19 10:37:57,640 - modules.command_executor - INFO - Executing gesture command 'open_palm' -> action 'pause_media'
2025-06-19 10:37:57,745 - modules.command_executor - INFO - Paused media
2025-06-19 10:37:57,746 - modules.gesture_control - INFO - Gesture 'open_palm' executed successfully
2025-06-19 10:38:00,158 - modules.gesture_control - INFO - Gesture detected: open_palm
2025-06-19 10:38:00,158 - modules.command_executor - INFO - Executing gesture command 'open_palm' -> action 'pause_media'
2025-06-19 10:38:00,262 - modules.command_executor - INFO - Paused media
2025-06-19 10:38:00,262 - modules.gesture_control - INFO - Gesture 'open_palm' executed successfully
2025-06-19 10:38:02,748 - modules.gesture_control - INFO - Gesture detected: thumbs_up
2025-06-19 10:38:02,748 - modules.command_executor - INFO - Executing gesture command 'thumbs_up' -> action 'volume_up'
2025-06-19 10:38:02,858 - modules.command_executor - INFO - Volume increased
2025-06-19 10:38:02,858 - modules.gesture_control - INFO - Gesture 'thumbs_up' executed successfully
2025-06-19 10:38:05,327 - modules.gesture_control - INFO - Gesture detected: thumbs_up
2025-06-19 10:38:05,327 - modules.command_executor - INFO - Executing gesture command 'thumbs_up' -> action 'volume_up'
2025-06-19 10:38:05,437 - modules.command_executor - INFO - Volume increased
2025-06-19 10:38:05,437 - modules.gesture_control - INFO - Gesture 'thumbs_up' executed successfully
2025-06-19 10:38:08,357 - modules.gesture_control - INFO - Gesture detected: thumbs_up
2025-06-19 10:38:08,358 - modules.command_executor - INFO - Executing gesture command 'thumbs_up' -> action 'volume_up'
2025-06-19 10:38:08,468 - modules.command_executor - INFO - Volume increased
2025-06-19 10:38:08,468 - modules.gesture_control - INFO - Gesture 'thumbs_up' executed successfully
2025-06-19 10:38:11,949 - modules.gesture_control - INFO - Gesture detected: thumbs_up
2025-06-19 10:38:11,949 - modules.command_executor - INFO - Executing gesture command 'thumbs_up' -> action 'volume_up'
2025-06-19 10:38:12,058 - modules.command_executor - INFO - Volume increased
2025-06-19 10:38:12,058 - modules.gesture_control - INFO - Gesture 'thumbs_up' executed successfully
2025-06-19 10:38:13,965 - modules.gesture_control - INFO - Gesture detected: thumbs_up
2025-06-19 10:38:13,966 - modules.command_executor - INFO - Executing gesture command 'thumbs_up' -> action 'volume_up'
2025-06-19 10:38:14,073 - modules.command_executor - INFO - Volume increased
2025-06-19 10:38:14,073 - modules.gesture_control - INFO - Gesture 'thumbs_up' executed successfully
2025-06-19 10:38:18,179 - modules.gesture_control - INFO - Gesture detected: thumbs_up
2025-06-19 10:38:18,180 - modules.command_executor - INFO - Executing gesture command 'thumbs_up' -> action 'volume_up'
2025-06-19 10:38:18,289 - modules.command_executor - INFO - Volume increased
2025-06-19 10:38:18,289 - modules.gesture_control - INFO - Gesture 'thumbs_up' executed successfully
2025-06-19 10:38:20,682 - modules.gesture_control - INFO - Gesture detected: fist
2025-06-19 10:38:20,682 - modules.command_executor - INFO - Executing gesture command 'fist' -> action 'play_media'
2025-06-19 10:38:20,787 - modules.command_executor - INFO - Played media
2025-06-19 10:38:20,787 - modules.gesture_control - INFO - Gesture 'fist' executed successfully
2025-06-19 10:38:31,703 - geventwebsocket.handler - INFO - 127.0.0.1 - - [2025-06-19 10:38:31] "GET /api/test/microphone HTTP/1.1" 200 217 0.000922
2025-06-19 10:38:53,533 - modules.gesture_control - INFO - Gesture detected: fist
2025-06-19 10:38:53,534 - modules.command_executor - INFO - Executing gesture command 'fist' -> action 'play_media'
2025-06-19 10:38:53,638 - modules.command_executor - INFO - Played media
2025-06-19 10:38:53,638 - modules.gesture_control - INFO - Gesture 'fist' executed successfully
2025-06-19 10:38:59,396 - modules.gesture_control - INFO - Gesture detected: thumbs_up
2025-06-19 10:38:59,396 - modules.command_executor - INFO - Executing gesture command 'thumbs_up' -> action 'volume_up'
2025-06-19 10:38:59,507 - modules.command_executor - INFO - Volume increased
2025-06-19 10:38:59,507 - modules.gesture_control - INFO - Gesture 'thumbs_up' executed successfully
2025-06-19 10:39:01,430 - modules.gesture_control - INFO - Gesture detected: thumbs_up
2025-06-19 10:39:01,431 - modules.command_executor - INFO - Executing gesture command 'thumbs_up' -> action 'volume_up'
2025-06-19 10:39:01,538 - modules.command_executor - INFO - Volume increased
2025-06-19 10:39:01,538 - modules.gesture_control - INFO - Gesture 'thumbs_up' executed successfully
2025-06-19 10:39:04,649 - modules.gesture_control - INFO - Gesture detected: thumbs_up
2025-06-19 10:39:04,650 - modules.command_executor - INFO - Executing gesture command 'thumbs_up' -> action 'volume_up'
2025-06-19 10:39:04,758 - modules.command_executor - INFO - Volume increased
2025-06-19 10:39:04,758 - modules.gesture_control - INFO - Gesture 'thumbs_up' executed successfully
2025-06-19 10:39:11,654 - __main__ - INFO - Gesture control disabled
2025-06-19 10:39:11,654 - geventwebsocket.handler - INFO - 127.0.0.1 - - [2025-06-19 10:39:11] "POST /api/control/toggle_gesture HTTP/1.1" 200 162 0.000824
2025-06-19 10:39:15,671 - __main__ - INFO - Gesture control enabled
2025-06-19 10:39:15,672 - geventwebsocket.handler - INFO - 127.0.0.1 - - [2025-06-19 10:39:15] "POST /api/control/toggle_gesture HTTP/1.1" 200 161 0.001000
2025-06-19 10:39:19,713 - modules.voice_control - INFO - Testing microphone... Say something!
2025-06-19 10:39:20,763 - modules.voice_control - ERROR - Error in listening loop: This audio source is already inside a context manager
2025-06-19 10:39:21,764 - modules.voice_control - ERROR - Error in listening loop: This audio source is already inside a context manager
2025-06-19 10:39:23,695 - geventwebsocket.handler - INFO - 127.0.0.1 - - [2025-06-19 10:39:23] "GET /api/test/microphone HTTP/1.1" 200 188 4.053912
2025-06-19 10:39:30,759 - modules.gesture_control - INFO - Gesture detected: thumbs_up
2025-06-19 10:39:30,760 - modules.command_executor - INFO - Executing gesture command 'thumbs_up' -> action 'volume_up'
2025-06-19 10:39:30,868 - modules.command_executor - INFO - Volume increased
2025-06-19 10:39:30,868 - modules.gesture_control - INFO - Gesture 'thumbs_up' executed successfully
2025-06-19 10:39:32,767 - modules.gesture_control - INFO - Gesture detected: thumbs_up
2025-06-19 10:39:32,767 - modules.command_executor - INFO - Executing gesture command 'thumbs_up' -> action 'volume_up'
2025-06-19 10:39:32,874 - modules.command_executor - INFO - Volume increased
2025-06-19 10:39:32,874 - modules.gesture_control - INFO - Gesture 'thumbs_up' executed successfully
2025-06-19 10:39:34,796 - modules.gesture_control - INFO - Gesture detected: thumbs_up
2025-06-19 10:39:34,796 - modules.command_executor - INFO - Executing gesture command 'thumbs_up' -> action 'volume_up'
2025-06-19 10:39:34,905 - modules.command_executor - INFO - Volume increased
2025-06-19 10:39:34,905 - modules.gesture_control - INFO - Gesture 'thumbs_up' executed successfully
2025-06-19 10:39:36,820 - modules.gesture_control - INFO - Gesture detected: thumbs_up
2025-06-19 10:39:36,822 - modules.command_executor - INFO - Executing gesture command 'thumbs_up' -> action 'volume_up'
2025-06-19 10:39:36,929 - modules.command_executor - INFO - Volume increased
2025-06-19 10:39:36,929 - modules.gesture_control - INFO - Gesture 'thumbs_up' executed successfully
2025-06-19 10:39:38,852 - modules.gesture_control - INFO - Gesture detected: thumbs_up
2025-06-19 10:39:38,852 - modules.command_executor - INFO - Executing gesture command 'thumbs_up' -> action 'volume_up'
2025-06-19 10:39:38,963 - modules.command_executor - INFO - Volume increased
2025-06-19 10:39:38,963 - modules.gesture_control - INFO - Gesture 'thumbs_up' executed successfully
2025-06-19 10:39:40,858 - modules.gesture_control - INFO - Gesture detected: thumbs_up
2025-06-19 10:39:40,858 - modules.command_executor - INFO - Executing gesture command 'thumbs_up' -> action 'volume_up'
2025-06-19 10:39:40,965 - modules.command_executor - INFO - Volume increased
2025-06-19 10:39:40,965 - modules.gesture_control - INFO - Gesture 'thumbs_up' executed successfully
2025-06-19 10:39:43,869 - modules.gesture_control - INFO - Gesture detected: thumbs_down
2025-06-19 10:39:43,870 - modules.command_executor - INFO - Executing gesture command 'thumbs_down' -> action 'volume_down'
2025-06-19 10:39:43,981 - modules.command_executor - INFO - Volume decreased
2025-06-19 10:39:43,981 - modules.gesture_control - INFO - Gesture 'thumbs_down' executed successfully
2025-06-19 10:39:54,606 - modules.gesture_control - INFO - Gesture detected: open_palm
2025-06-19 10:39:54,607 - modules.command_executor - INFO - Executing gesture command 'open_palm' -> action 'pause_media'
2025-06-19 10:39:54,711 - modules.command_executor - INFO - Paused media
2025-06-19 10:39:54,711 - modules.gesture_control - INFO - Gesture 'open_palm' executed successfully
2025-06-19 10:39:56,646 - modules.gesture_control - INFO - Gesture detected: open_palm
2025-06-19 10:39:56,647 - modules.command_executor - INFO - Executing gesture command 'open_palm' -> action 'pause_media'
2025-06-19 10:39:56,751 - modules.command_executor - INFO - Paused media
2025-06-19 10:39:56,752 - modules.gesture_control - INFO - Gesture 'open_palm' executed successfully
2025-06-19 10:39:58,682 - modules.gesture_control - INFO - Gesture detected: open_palm
2025-06-19 10:39:58,683 - modules.command_executor - INFO - Executing gesture command 'open_palm' -> action 'pause_media'
2025-06-19 10:39:58,786 - modules.command_executor - INFO - Paused media
2025-06-19 10:39:58,786 - modules.gesture_control - INFO - Gesture 'open_palm' executed successfully
2025-06-19 10:40:00,700 - modules.gesture_control - INFO - Gesture detected: open_palm
2025-06-19 10:40:00,700 - modules.command_executor - INFO - Executing gesture command 'open_palm' -> action 'pause_media'
2025-06-19 10:40:00,804 - modules.command_executor - INFO - Paused media
2025-06-19 10:40:00,804 - modules.gesture_control - INFO - Gesture 'open_palm' executed successfully
2025-06-19 10:40:02,746 - modules.gesture_control - INFO - Gesture detected: open_palm
2025-06-19 10:40:02,747 - modules.command_executor - INFO - Executing gesture command 'open_palm' -> action 'pause_media'
2025-06-19 10:40:02,849 - modules.command_executor - INFO - Paused media
2025-06-19 10:40:02,849 - modules.gesture_control - INFO - Gesture 'open_palm' executed successfully
2025-06-19 10:40:04,759 - modules.gesture_control - INFO - Gesture detected: open_palm
2025-06-19 10:40:04,759 - modules.command_executor - INFO - Executing gesture command 'open_palm' -> action 'pause_media'
2025-06-19 10:40:04,864 - modules.command_executor - INFO - Paused media
2025-06-19 10:40:04,864 - modules.gesture_control - INFO - Gesture 'open_palm' executed successfully
2025-06-19 10:40:16,498 - modules.gesture_control - INFO - Gesture detected: open_palm
2025-06-19 10:40:16,498 - modules.command_executor - INFO - Executing gesture command 'open_palm' -> action 'pause_media'
2025-06-19 10:40:16,610 - modules.command_executor - INFO - Paused media
2025-06-19 10:40:16,610 - modules.gesture_control - INFO - Gesture 'open_palm' executed successfully
2025-06-19 10:40:19,518 - modules.gesture_control - INFO - Gesture detected: thumbs_up
2025-06-19 10:40:19,518 - modules.command_executor - INFO - Executing gesture command 'thumbs_up' -> action 'volume_up'
2025-06-19 10:40:19,630 - modules.command_executor - INFO - Volume increased
2025-06-19 10:40:19,630 - modules.gesture_control - INFO - Gesture 'thumbs_up' executed successfully
2025-06-19 10:40:23,046 - modules.gesture_control - INFO - Gesture detected: thumbs_down
2025-06-19 10:40:23,052 - modules.command_executor - INFO - Executing gesture command 'thumbs_down' -> action 'volume_down'
2025-06-19 10:40:23,161 - modules.command_executor - INFO - Volume decreased
2025-06-19 10:40:23,161 - modules.gesture_control - INFO - Gesture 'thumbs_down' executed successfully
2025-06-19 10:59:49,465 - modules.activity_manager - INFO - Activity Manager initialized
2025-06-19 10:59:49,465 - modules.command_executor - INFO - Command Executor initialized
2025-06-19 10:59:49,654 - modules.voice_control - INFO - Adjusting for ambient noise... Please wait.
2025-06-19 10:59:51,705 - modules.voice_control - INFO - Microphone initialized successfully
2025-06-19 10:59:51,705 - modules.voice_control - INFO - Voice Controller initialized
2025-06-19 10:59:51,711 - modules.gesture_control - INFO - Gesture Controller initialized
2025-06-19 10:59:51,752 - modules.web_interface - INFO - Web Interface initialized
2025-06-19 10:59:51,773 - modules.system_tray - INFO - System tray icon created successfully
2025-06-19 10:59:51,774 - modules.system_tray - INFO - System Tray Application initialized
2025-06-19 10:59:51,774 - __main__ - INFO - Starting Voice and Gesture Control System
2025-06-19 10:59:51,775 - modules.web_interface - INFO - Status update thread started
2025-06-19 10:59:51,775 - modules.voice_control - INFO - Starting voice recognition...
2025-06-19 10:59:51,776 - modules.web_interface - INFO - Starting web interface on 127.0.0.1:5000
2025-06-19 10:59:51,776 - __main__ - INFO - All modules started successfully
2025-06-19 10:59:51,777 - modules.voice_control - INFO - Voice recognition started
2025-06-19 10:59:51,777 - modules.system_tray - INFO - Starting system tray application
2025-06-19 10:59:55,280 - modules.gesture_control - INFO - Camera 0 initialized successfully
2025-06-19 10:59:55,281 - modules.gesture_control - INFO - Starting gesture recognition...
2025-06-19 10:59:55,282 - modules.gesture_control - INFO - Gesture recognition started
2025-06-19 11:00:13,708 - geventwebsocket.handler - INFO - 127.0.0.1 - - [2025-06-19 11:00:13] "GET / HTTP/1.1" 200 29559 0.013677
2025-06-19 11:00:13,865 - geventwebsocket.handler - INFO - 127.0.0.1 - - [2025-06-19 11:00:13] "GET /socket.io/?EIO=4&transport=polling&t=PU6gete HTTP/1.1" 200 276 0.001239
2025-06-19 11:00:13,931 - geventwebsocket.handler - INFO - 127.0.0.1 - - [2025-06-19 11:00:13] "GET /api/commands HTTP/1.1" 200 286 0.000000
2025-06-19 11:00:13,934 - modules.web_interface - INFO - Client connected to SocketIO
2025-06-19 11:00:13,934 - geventwebsocket.handler - INFO - 127.0.0.1 - - [2025-06-19 11:00:13] "POST /socket.io/?EIO=4&transport=polling&t=PU6geui&sid=JKsxM8X-8uWNLoq2AAAA HTTP/1.1" 200 195 0.001045
2025-06-19 11:00:13,935 - geventwebsocket.handler - INFO - 127.0.0.1 - - [2025-06-19 11:00:13] "GET /socket.io/?EIO=4&transport=polling&t=PU6geuk&sid=JKsxM8X-8uWNLoq2AAAA HTTP/1.1" 200 549 0.000000
2025-06-19 11:00:13,941 - geventwebsocket.handler - INFO - 127.0.0.1 - - [2025-06-19 11:00:13] "POST /socket.io/?EIO=4&transport=polling&t=PU6geup&sid=JKsxM8X-8uWNLoq2AAAA HTTP/1.1" 200 195 0.000000
2025-06-19 11:00:13,942 - geventwebsocket.handler - INFO - 127.0.0.1 - - [2025-06-19 11:00:13] "GET /socket.io/?EIO=4&transport=polling&t=PU6geup.0&sid=JKsxM8X-8uWNLoq2AAAA HTTP/1.1" 200 157 0.000000
2025-06-19 11:00:13,943 - geventwebsocket.handler - INFO - 127.0.0.1 - - [2025-06-19 11:00:13] "GET /api/commands HTTP/1.1" 200 286 0.000000
2025-06-19 11:00:13,945 - geventwebsocket.handler - INFO - 127.0.0.1 - - [2025-06-19 11:00:13] "GET /favicon.ico HTTP/1.1" 404 331 0.001156
2025-06-19 11:00:47,705 - geventwebsocket.handler - INFO - 127.0.0.1 - - [2025-06-19 11:00:47] "GET /api/status HTTP/1.1" 200 612 0.000000
2025-06-19 11:00:47,708 - geventwebsocket.handler - INFO - 127.0.0.1 - - [2025-06-19 11:00:47] "GET /api/activities HTTP/1.1" 200 110 0.001000
2025-06-19 11:00:47,710 - geventwebsocket.handler - INFO - 127.0.0.1 - - [2025-06-19 11:00:47] "GET /api/activities/stats HTTP/1.1" 200 228 0.001506
2025-06-19 11:00:47,711 - geventwebsocket.handler - INFO - 127.0.0.1 - - [2025-06-19 11:00:47] "GET /api/activities/search?q=gesture HTTP/1.1" 200 110 0.000000
2025-06-19 11:00:47,713 - geventwebsocket.handler - INFO - 127.0.0.1 - - [2025-06-19 11:00:47] "GET /api/activities/recent?minutes=60 HTTP/1.1" 200 110 0.000000
2025-06-19 11:00:50,510 - modules.gesture_control - INFO - Gesture detected: thumbs_up
2025-06-19 11:00:50,510 - modules.command_executor - INFO - Executing gesture command 'thumbs_up' -> action 'volume_up'
2025-06-19 11:00:50,621 - modules.command_executor - INFO - Volume increased
2025-06-19 11:01:08,574 - geventwebsocket.handler - INFO - 127.0.0.1 - - [2025-06-19 11:01:08] "GET /api/status HTTP/1.1" 200 617 0.000000
2025-06-19 11:12:25,203 - modules.activity_manager - INFO - Auto-save thread started
2025-06-19 11:12:25,203 - modules.activity_manager - INFO - Activity Manager initialized with storage at data
2025-06-19 11:12:25,203 - modules.image_capture - INFO - Image Capture initialized - Storage: data\images
2025-06-19 11:12:25,203 - modules.command_executor - INFO - Command Executor initialized
2025-06-19 11:12:25,398 - modules.voice_control - INFO - Adjusting for ambient noise... Please wait.
2025-06-19 11:12:27,435 - modules.voice_control - INFO - Microphone initialized successfully
2025-06-19 11:12:27,435 - modules.voice_control - INFO - Voice Controller initialized
2025-06-19 11:12:27,451 - modules.gesture_control - INFO - Gesture Controller initialized
2025-06-19 11:12:27,498 - modules.web_interface - INFO - Web Interface initialized
2025-06-19 11:12:27,519 - modules.system_tray - INFO - System tray icon created successfully
2025-06-19 11:12:27,519 - modules.system_tray - INFO - System Tray Application initialized
2025-06-19 11:12:27,519 - __main__ - INFO - Starting Voice and Gesture Control System
2025-06-19 11:12:27,520 - modules.web_interface - INFO - Status update thread started
2025-06-19 11:12:27,520 - modules.voice_control - INFO - Starting voice recognition...
2025-06-19 11:12:27,520 - modules.web_interface - INFO - Starting web interface on 127.0.0.1:5000
2025-06-19 11:12:27,521 - __main__ - INFO - All modules started successfully
2025-06-19 11:12:27,522 - modules.voice_control - INFO - Voice recognition started
2025-06-19 11:12:27,522 - modules.system_tray - INFO - Starting system tray application
2025-06-19 11:12:27,522 - modules.image_capture - INFO - Image capture started
2025-06-19 11:12:31,236 - modules.gesture_control - INFO - Camera 0 initialized successfully
2025-06-19 11:12:31,236 - modules.gesture_control - INFO - Starting gesture recognition...
2025-06-19 11:12:31,237 - modules.gesture_control - INFO - Gesture recognition started
2025-06-19 11:12:48,312 - modules.gesture_control - INFO - Gesture detected: thumbs_up
2025-06-19 11:12:48,312 - modules.command_executor - INFO - Executing gesture command 'thumbs_up' -> action 'volume_up'
2025-06-19 11:12:48,421 - modules.command_executor - INFO - Volume increased
2025-06-19 11:12:54,454 - geventwebsocket.handler - INFO - 127.0.0.1 - - [2025-06-19 11:12:54] "GET / HTTP/1.1" 200 47676 0.006999
2025-06-19 11:12:54,695 - geventwebsocket.handler - INFO - 127.0.0.1 - - [2025-06-19 11:12:54] "GET /socket.io/?EIO=4&transport=polling&t=PU6jYdb HTTP/1.1" 200 276 0.000988
2025-06-19 11:12:54,756 - modules.web_interface - INFO - Client connected to SocketIO
2025-06-19 11:12:54,757 - geventwebsocket.handler - INFO - 127.0.0.1 - - [2025-06-19 11:12:54] "POST /socket.io/?EIO=4&transport=polling&t=PU6jYeY&sid=LZx86FlYGDma76ekAAAA HTTP/1.1" 200 195 0.001737
2025-06-19 11:12:54,758 - geventwebsocket.handler - INFO - 127.0.0.1 - - [2025-06-19 11:12:54] "GET /socket.io/?EIO=4&transport=polling&t=PU6jYeZ&sid=LZx86FlYGDma76ekAAAA HTTP/1.1" 200 554 0.000000
2025-06-19 11:12:54,760 - geventwebsocket.handler - INFO - 127.0.0.1 - - [2025-06-19 11:12:54] "GET /api/images?count=20 HTTP/1.1" 200 305 0.000998
2025-06-19 11:12:54,761 - geventwebsocket.handler - INFO - 127.0.0.1 - - [2025-06-19 11:12:54] "GET /api/commands HTTP/1.1" 200 286 0.000000
2025-06-19 11:12:54,816 - geventwebsocket.handler - INFO - 127.0.0.1 - - [2025-06-19 11:12:54] "POST /socket.io/?EIO=4&transport=polling&t=PU6jYfV&sid=LZx86FlYGDma76ekAAAA HTTP/1.1" 200 195 0.001000
