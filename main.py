#!/usr/bin/env python3
"""
Voice and Gesture Control System - Main Entry Point
"""

import sys
import os
import logging
import yaml
import threading
import time
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from modules.voice_control import VoiceController
from modules.gesture_control import GestureController
from modules.command_executor import CommandExecutor
from modules.system_tray import SystemTrayApp
from modules.web_interface import WebInterface

class VoiceGestureControlSystem:
    """Main application class that coordinates all modules"""
    
    def __init__(self, config_path="config.yaml"):
        self.config_path = config_path
        self.config = self.load_config()
        self.setup_logging()
        
        # Initialize components
        self.command_executor = CommandExecutor(self.config)
        self.voice_controller = VoiceController(self.config, self.command_executor)
        self.gesture_controller = GestureController(self.config, self.command_executor)
        self.web_interface = WebInterface(self.config, self)
        
        # Control flags
        self.running = False
        self.voice_enabled = True
        self.gesture_enabled = True
        
        self.logger = logging.getLogger(__name__)
        
    def load_config(self):
        """Load configuration from YAML file"""
        try:
            with open(self.config_path, 'r') as file:
                return yaml.safe_load(file)
        except FileNotFoundError:
            self.logger.error(f"Configuration file {self.config_path} not found")
            return self.get_default_config()
        except yaml.YAMLError as e:
            self.logger.error(f"Error parsing configuration file: {e}")
            return self.get_default_config()
    
    def get_default_config(self):
        """Return default configuration"""
        return {
            'voice': {'wake_word': 'computer', 'language': 'en-US'},
            'gesture': {'camera_index': 0, 'detection_confidence': 0.7},
            'system': {'auto_start': False, 'minimize_to_tray': True},
            'web': {'host': '127.0.0.1', 'port': 5000},
            'logging': {'level': 'INFO'}
        }
    
    def setup_logging(self):
        """Setup logging configuration"""
        log_level = getattr(logging, self.config.get('logging', {}).get('level', 'INFO'))
        
        # Create logs directory if it doesn't exist
        os.makedirs('logs', exist_ok=True)
        
        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('logs/voice_gesture_control.log'),
                logging.StreamHandler(sys.stdout)
            ]
        )
    
    def start(self):
        """Start the voice and gesture control system"""
        if self.running:
            return
            
        self.logger.info("Starting Voice and Gesture Control System")
        self.running = True
        
        # Start web interface in separate thread
        web_thread = threading.Thread(target=self.web_interface.run, daemon=True)
        web_thread.start()
        
        # Start voice control in separate thread
        if self.voice_enabled:
            voice_thread = threading.Thread(target=self.voice_controller.start, daemon=True)
            voice_thread.start()
        
        # Start gesture control in separate thread
        if self.gesture_enabled:
            gesture_thread = threading.Thread(target=self.gesture_controller.start, daemon=True)
            gesture_thread.start()
        
        self.logger.info("All modules started successfully")
    
    def stop(self):
        """Stop the voice and gesture control system"""
        if not self.running:
            return
            
        self.logger.info("Stopping Voice and Gesture Control System")
        self.running = False
        
        # Stop all controllers
        if hasattr(self.voice_controller, 'stop'):
            self.voice_controller.stop()
        if hasattr(self.gesture_controller, 'stop'):
            self.gesture_controller.stop()
        if hasattr(self.web_interface, 'stop'):
            self.web_interface.stop()
        
        self.logger.info("System stopped successfully")
    
    def toggle_voice_control(self):
        """Toggle voice control on/off"""
        self.voice_enabled = not self.voice_enabled
        self.logger.info(f"Voice control {'enabled' if self.voice_enabled else 'disabled'}")
    
    def toggle_gesture_control(self):
        """Toggle gesture control on/off"""
        self.gesture_enabled = not self.gesture_enabled
        self.logger.info(f"Gesture control {'enabled' if self.gesture_enabled else 'disabled'}")

def main():
    """Main entry point"""
    app = None
    try:
        print("Starting Voice and Gesture Control System...")
        print("Press Ctrl+C to stop the system")

        # Create main application
        app = VoiceGestureControlSystem()

        # Create and start system tray
        tray_app = SystemTrayApp(app)

        # Start the main system
        app.start()

        print("System started successfully!")
        print("- Web interface: http://127.0.0.1:5000")
        print("- System tray: Right-click the tray icon for controls")
        print("- Voice: Say 'computer' followed by a command")
        print("- Gestures: Show hand gestures to the camera")

        # Run system tray (this blocks)
        tray_app.run()

    except KeyboardInterrupt:
        print("\n\n🛑 Keyboard interrupt received (Ctrl+C)")
        print("Shutting down gracefully...")
    except Exception as e:
        print(f"\n❌ Error: {e}")
        logging.exception("Unexpected error occurred")
    finally:
        if app:
            print("Stopping all modules...")
            app.stop()
        print("✅ System shutdown complete")

if __name__ == "__main__":
    main()
