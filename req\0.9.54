Collecting pyautogui
  Downloading PyAutoGUI-0.9.54.tar.gz (61 kB)
  Installing build dependencies: started
  Installing build dependencies: finished with status 'done'
  Getting requirements to build wheel: started
  Getting requirements to build wheel: finished with status 'done'
  Preparing metadata (pyproject.toml): started
  Preparing metadata (pyproject.toml): finished with status 'done'
Collecting pymsgbox (from pyautogui)
  Downloading PyMsgBox-1.0.9.tar.gz (18 kB)
  Installing build dependencies: started
  Installing build dependencies: finished with status 'done'
  Getting requirements to build wheel: started
  Getting requirements to build wheel: finished with status 'done'
  Preparing metadata (pyproject.toml): started
  Preparing metadata (pyproject.toml): finished with status 'done'
Collecting pytweening>=1.0.4 (from pyautogui)
  Downloading pytweening-1.2.0.tar.gz (171 kB)
  Preparing metadata (setup.py): started
  Preparing metadata (setup.py): finished with status 'done'
Collecting pyscreeze>=0.1.21 (from pyautogui)
  Downloading pyscreeze-1.0.1.tar.gz (27 kB)
  Installing build dependencies: started
  Installing build dependencies: finished with status 'done'
  Getting requirements to build wheel: started
  Getting requirements to build wheel: finished with status 'done'
  Preparing metadata (pyproject.toml): started
  Preparing metadata (pyproject.toml): finished with status 'done'
Collecting pygetwindow>=0.0.5 (from pyautogui)
  Downloading PyGetWindow-0.0.9.tar.gz (9.7 kB)
  Preparing metadata (setup.py): started
  Preparing metadata (setup.py): finished with status 'done'
Collecting mouseinfo (from pyautogui)
  Downloading MouseInfo-0.1.3.tar.gz (10 kB)
  Preparing metadata (setup.py): started
  Preparing metadata (setup.py): finished with status 'done'
Collecting pyrect (from pygetwindow>=0.0.5->pyautogui)
  Downloading PyRect-0.2.0.tar.gz (17 kB)
  Preparing metadata (setup.py): started
  Preparing metadata (setup.py): finished with status 'done'
Collecting pyperclip (from mouseinfo->pyautogui)
  Downloading pyperclip-1.9.0.tar.gz (20 kB)
  Preparing metadata (setup.py): started
  Preparing metadata (setup.py): finished with status 'done'
Building wheels for collected packages: pyautogui, pygetwindow, pyscreeze, pytweening, mouseinfo, pymsgbox, pyperclip, pyrect
  Building wheel for pyautogui (pyproject.toml): started
  Building wheel for pyautogui (pyproject.toml): finished with status 'done'
  Created wheel for pyautogui: filename=pyautogui-0.9.54-py3-none-any.whl size=37707 sha256=cd7ad1989755f432ee0ef037659c308bd4d33526ba44a8df4b98836f9e0d1533
  Stored in directory: c:\users\<USER>\appdata\local\pip\cache\wheels\d9\d6\47\04075995b093ecc87c212c9a3dbd34e59456c6fe504d65c3e4
  Building wheel for pygetwindow (setup.py): started
  Building wheel for pygetwindow (setup.py): finished with status 'done'
  Created wheel for pygetwindow: filename=pygetwindow-0.0.9-py3-none-any.whl size=11136 sha256=252b3c3fd3c9821fc7baf27b1ea9da24c8d939da4b6f953ffa853451804d8d59
  Stored in directory: c:\users\<USER>\appdata\local\pip\cache\wheels\b3\39\81\34dd7a2eca5f885f1f6e2796761970daf66a2d98ac1904f5f4
  Building wheel for pyscreeze (pyproject.toml): started
  Building wheel for pyscreeze (pyproject.toml): finished with status 'done'
  Created wheel for pyscreeze: filename=pyscreeze-1.0.1-py3-none-any.whl size=14481 sha256=f2176bb7a0067e9e299b0643a96a3fb2574f13a554f68e9b89aaed7135e3b4c8
  Stored in directory: c:\users\<USER>\appdata\local\pip\cache\wheels\cd\3a\c2\7f2839239a069aa3c9564f6777cbb29d733720ef673f104f0d
  Building wheel for pytweening (setup.py): started
  Building wheel for pytweening (setup.py): finished with status 'done'
  Created wheel for pytweening: filename=pytweening-1.2.0-py3-none-any.whl size=8135 sha256=7784af78dfa3a1b32e2cb8654416967efed6ab2ed8f5d96d35d798bdccffea04
  Stored in directory: c:\users\<USER>\appdata\local\pip\cache\wheels\23\d5\13\4e9bdadbfe3c78e47c675e7410c0eed2fbb63c5ea6cf1b40e7
  Building wheel for mouseinfo (setup.py): started
  Building wheel for mouseinfo (setup.py): finished with status 'done'
  Created wheel for mouseinfo: filename=mouseinfo-0.1.3-py3-none-any.whl size=10966 sha256=56a46cf33d1db06d0f024cced4577d29c9ddb28f94329355eddd0dad469eec49
  Stored in directory: c:\users\<USER>\appdata\local\pip\cache\wheels\b1\9b\f3\08650eb7f00af32f07789f3c6a101e0d7fc762b9891ae843bb
  Building wheel for pymsgbox (pyproject.toml): started
  Building wheel for pymsgbox (pyproject.toml): finished with status 'done'
  Created wheel for pymsgbox: filename=pymsgbox-1.0.9-py3-none-any.whl size=7467 sha256=c4126802db699d1a75204e650e7d3d290abc7b2450e79e4eef88521d426e6138
  Stored in directory: c:\users\<USER>\appdata\local\pip\cache\wheels\55\e7\aa\239163543708d1e15c3d9a1b89dbfe3954b0929a6df2951b83
  Building wheel for pyperclip (setup.py): started
  Building wheel for pyperclip (setup.py): finished with status 'done'
  Created wheel for pyperclip: filename=pyperclip-1.9.0-py3-none-any.whl size=11116 sha256=8ce5290dc8d96d958aa1abfb73200b0abfb88ff07f403383e9bdaf0f2310ad67
  Stored in directory: c:\users\<USER>\appdata\local\pip\cache\wheels\e0\e8\fc\8ab8aa326e33bc066ccd5f3ca9646eab4299881af933f94f09
  Building wheel for pyrect (setup.py): started
  Building wheel for pyrect (setup.py): finished with status 'done'
  Created wheel for pyrect: filename=pyrect-0.2.0-py2.py3-none-any.whl size=11305 sha256=83abb61cc4dcf7a728e8e34f3bdd7d7fd0432d82185edf92bd06277f06c89d8b
  Stored in directory: c:\users\<USER>\appdata\local\pip\cache\wheels\0b\1e\d7\0c74bd8f60b39c14d84e307398786002aa7ddc905927cc03c5
Successfully built pyautogui pygetwindow pyscreeze pytweening mouseinfo pymsgbox pyperclip pyrect
Installing collected packages: pytweening, pyscreeze, pyrect, pyperclip, pymsgbox, pygetwindow, mouseinfo, pyautogui

Successfully installed mouseinfo-0.1.3 pyautogui-0.9.54 pygetwindow-0.0.9 pymsgbox-1.0.9 pyperclip-1.9.0 pyrect-0.2.0 pyscreeze-1.0.1 pytweening-1.2.0
