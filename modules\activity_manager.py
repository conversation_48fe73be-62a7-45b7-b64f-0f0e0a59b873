"""
Activity Manager Module
Tracks and manages all user activities including gestures, voice commands, and system actions
"""

import logging
import time
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from collections import deque
from dataclasses import dataclass, asdict
import json

@dataclass
class Activity:
    """Represents a single user activity"""
    id: str
    timestamp: float
    type: str  # 'gesture', 'voice', 'system'
    action: str
    details: Dict[str, Any]
    success: bool
    duration: Optional[float] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert activity to dictionary"""
        data = asdict(self)
        data['datetime'] = datetime.fromtimestamp(self.timestamp).isoformat()
        data['time_ago'] = self._get_time_ago()
        return data
    
    def _get_time_ago(self) -> str:
        """Get human-readable time ago string"""
        now = time.time()
        diff = now - self.timestamp
        
        if diff < 60:
            return f"{int(diff)} seconds ago"
        elif diff < 3600:
            return f"{int(diff // 60)} minutes ago"
        elif diff < 86400:
            return f"{int(diff // 3600)} hours ago"
        else:
            return f"{int(diff // 86400)} days ago"

class ActivityManager:
    """Manages all user activities and provides real-time tracking"""
    
    def __init__(self, max_activities: int = 1000):
        self.logger = logging.getLogger(__name__)
        self.max_activities = max_activities
        
        # Activity storage
        self.activities = deque(maxlen=max_activities)
        self.activity_counter = 0
        
        # Thread safety
        self.lock = threading.Lock()
        
        # Event callbacks for real-time updates
        self.activity_callbacks = []
        
        # Statistics
        self.stats = {
            'total_activities': 0,
            'gesture_count': 0,
            'voice_count': 0,
            'system_count': 0,
            'success_rate': 0.0,
            'last_activity_time': None
        }
        
        self.logger.info("Activity Manager initialized")
    
    def add_activity(self, activity_type: str, action: str, details: Dict[str, Any] = None, 
                    success: bool = True, duration: Optional[float] = None) -> str:
        """Add a new activity"""
        with self.lock:
            # Generate unique ID
            self.activity_counter += 1
            activity_id = f"act_{self.activity_counter}_{int(time.time())}"
            
            # Create activity
            activity = Activity(
                id=activity_id,
                timestamp=time.time(),
                type=activity_type,
                action=action,
                details=details or {},
                success=success,
                duration=duration
            )
            
            # Add to storage
            self.activities.append(activity)
            
            # Update statistics
            self._update_stats(activity)
            
            # Notify callbacks
            self._notify_callbacks(activity)
            
            self.logger.info(f"Activity added: {activity_type} - {action} ({'success' if success else 'failed'})")
            return activity_id
    
    def get_activities(self, limit: Optional[int] = None, 
                      activity_type: Optional[str] = None,
                      since: Optional[float] = None) -> List[Dict[str, Any]]:
        """Get activities with optional filtering"""
        with self.lock:
            activities = list(self.activities)
            
            # Filter by type
            if activity_type:
                activities = [a for a in activities if a.type == activity_type]
            
            # Filter by time
            if since:
                activities = [a for a in activities if a.timestamp >= since]
            
            # Sort by timestamp (newest first)
            activities.sort(key=lambda x: x.timestamp, reverse=True)
            
            # Apply limit
            if limit:
                activities = activities[:limit]
            
            return [activity.to_dict() for activity in activities]
    
    def get_recent_activities(self, minutes: int = 10) -> List[Dict[str, Any]]:
        """Get activities from the last N minutes"""
        since = time.time() - (minutes * 60)
        return self.get_activities(since=since)
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get activity statistics"""
        with self.lock:
            return self.stats.copy()
    
    def clear_activities(self):
        """Clear all activities"""
        with self.lock:
            self.activities.clear()
            self.activity_counter = 0
            self._reset_stats()
            self.logger.info("All activities cleared")
    
    def add_callback(self, callback):
        """Add callback for real-time activity updates"""
        self.activity_callbacks.append(callback)
    
    def remove_callback(self, callback):
        """Remove activity callback"""
        if callback in self.activity_callbacks:
            self.activity_callbacks.remove(callback)
    
    def _update_stats(self, activity: Activity):
        """Update activity statistics"""
        self.stats['total_activities'] += 1
        self.stats['last_activity_time'] = activity.timestamp
        
        # Count by type
        if activity.type == 'gesture':
            self.stats['gesture_count'] += 1
        elif activity.type == 'voice':
            self.stats['voice_count'] += 1
        elif activity.type == 'system':
            self.stats['system_count'] += 1
        
        # Calculate success rate
        total_activities = len(self.activities)
        successful_activities = sum(1 for a in self.activities if a.success)
        self.stats['success_rate'] = (successful_activities / total_activities * 100) if total_activities > 0 else 0
    
    def _reset_stats(self):
        """Reset statistics"""
        self.stats = {
            'total_activities': 0,
            'gesture_count': 0,
            'voice_count': 0,
            'system_count': 0,
            'success_rate': 0.0,
            'last_activity_time': None
        }
    
    def _notify_callbacks(self, activity: Activity):
        """Notify all callbacks of new activity"""
        for callback in self.activity_callbacks:
            try:
                callback(activity.to_dict())
            except Exception as e:
                self.logger.error(f"Error in activity callback: {e}")
    
    def search_activities(self, query: str, limit: int = 50) -> List[Dict[str, Any]]:
        """Search activities by action or details"""
        with self.lock:
            query_lower = query.lower()
            matching_activities = []
            
            for activity in self.activities:
                # Search in action
                if query_lower in activity.action.lower():
                    matching_activities.append(activity)
                    continue
                
                # Search in details
                details_str = json.dumps(activity.details).lower()
                if query_lower in details_str:
                    matching_activities.append(activity)
            
            # Sort by timestamp (newest first)
            matching_activities.sort(key=lambda x: x.timestamp, reverse=True)
            
            # Apply limit
            if limit:
                matching_activities = matching_activities[:limit]
            
            return [activity.to_dict() for activity in matching_activities]
    
    def get_activity_by_id(self, activity_id: str) -> Optional[Dict[str, Any]]:
        """Get specific activity by ID"""
        with self.lock:
            for activity in self.activities:
                if activity.id == activity_id:
                    return activity.to_dict()
            return None
